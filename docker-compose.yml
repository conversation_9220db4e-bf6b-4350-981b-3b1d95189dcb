version: '3.8'

services:
  # PostgreSQL Database (for local development)
  postgres:
    image: postgres:15-alpine
    container_name: trend-platform-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: trend_platform
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/migrations:/docker-entrypoint-initdb.d
    networks:
      - trend_network

  # Redis Cache and Message Broker
  redis:
    image: redis:7-alpine
    container_name: trend-platform-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - trend_network

  # FastAPI Backend Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: trend-platform-backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=********************************************/trend_platform
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/0
      - ENVIRONMENT=development
      - DEBUG=true
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    networks:
      - trend_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Celery Worker
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: trend-platform-celery-worker
    restart: unless-stopped
    environment:
      - DATABASE_URL=********************************************/trend_platform
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/0
      - ENVIRONMENT=development
      - DEBUG=true
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - celery_logs:/app/logs
    networks:
      - trend_network
    command: celery -A app.celery_app worker --loglevel=info

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: trend-platform-celery-beat
    restart: unless-stopped
    environment:
      - DATABASE_URL=********************************************/trend_platform
      - REDIS_URL=redis://:redis_password@redis:6379/0
      - CELERY_BROKER_URL=redis://:redis_password@redis:6379/0
      - ENVIRONMENT=development
      - DEBUG=true
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - celery_logs:/app/logs
    networks:
      - trend_network
    command: celery -A app.celery_app beat --loglevel=info

  # Next.js Frontend Dashboard
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: trend-platform-frontend
    restart: unless-stopped
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXTAUTH_URL=http://localhost:3000
      - NODE_ENV=development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    networks:
      - trend_network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:
  backend_logs:
  celery_logs:

networks:
  trend_network:
    driver: bridge
