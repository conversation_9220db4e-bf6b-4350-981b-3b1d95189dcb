"""
Authentication API endpoints.

Handles user authentication, registration, and profile management
using JWT tokens and Supabase authentication.
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from datetime import datetime, timedelta
from jose import jwt
import structlog

from ..config import settings
from ..dependencies import (
    get_user_repository,
    get_current_user,
    get_current_active_user,
    security
)
from database.models.user_model import UserProfileRepository

logger = structlog.get_logger()

router = APIRouter(prefix="/auth")


class LoginRequest(BaseModel):
    """Login request model."""
    email: EmailStr
    password: str


class RegisterRequest(BaseModel):
    """Registration request model."""
    email: EmailStr
    password: str
    role: str = "viewer"


class TokenResponse(BaseModel):
    """Token response model."""
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: Dict[str, Any]


class UserProfileResponse(BaseModel):
    """User profile response model."""
    id: str
    email: str
    role: str
    permissions: list
    created_at: datetime
    updated_at: datetime


def create_access_token(data: Dict[str, Any]) -> str:
    """Create JWT access token."""
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=settings.jwt_access_token_expire_minutes)
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(
        to_encode,
        settings.jwt_secret_key,
        algorithm=settings.jwt_algorithm
    )
    return encoded_jwt


@router.post("/login", response_model=TokenResponse)
async def login(
    request: LoginRequest,
    user_repo: UserProfileRepository = Depends(get_user_repository)
):
    """Authenticate user and return access token."""
    # TODO: Implement actual authentication with Supabase
    # For now, this is a placeholder implementation
    
    logger.info("Login attempt", email=request.email)
    
    # In a real implementation, you would:
    # 1. Verify credentials with Supabase Auth
    # 2. Get user profile from database
    # 3. Create JWT token with user information
    
    # Placeholder: Create a mock user for development
    if request.email == "<EMAIL>" and request.password == "admin123":
        user_data = {
            "id": "00000000-0000-0000-0000-000000000001",
            "email": request.email,
            "role": "admin",
            "permissions": ["read", "write", "admin"],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Create access token
        token_data = {"sub": user_data["id"], "email": user_data["email"]}
        access_token = create_access_token(token_data)
        
        logger.info("Login successful", user_id=user_data["id"], email=request.email)
        
        return TokenResponse(
            access_token=access_token,
            expires_in=settings.jwt_access_token_expire_minutes * 60,
            user=user_data
        )
    
    logger.warning("Login failed - invalid credentials", email=request.email)
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Incorrect email or password",
        headers={"WWW-Authenticate": "Bearer"},
    )


@router.post("/register", response_model=TokenResponse)
async def register(
    request: RegisterRequest,
    user_repo: UserProfileRepository = Depends(get_user_repository)
):
    """Register new user account."""
    logger.info("Registration attempt", email=request.email, role=request.role)
    
    # TODO: Implement actual registration with Supabase
    # For now, this is a placeholder implementation
    
    # Validate role
    valid_roles = ["viewer", "editor", "admin"]
    if request.role not in valid_roles:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid role. Must be one of: {valid_roles}"
        )
    
    # In a real implementation, you would:
    # 1. Create user in Supabase Auth
    # 2. Create user profile in database
    # 3. Send verification email
    # 4. Return access token
    
    # Placeholder implementation
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="Registration not yet implemented. Please use Supabase Auth directly."
    )


@router.get("/me", response_model=UserProfileResponse)
async def get_current_user_profile(
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """Get current user profile."""
    logger.info("Get user profile", user_id=current_user["id"])
    
    return UserProfileResponse(
        id=current_user["id"],
        email=current_user.get("email", ""),
        role=current_user.get("role", "viewer"),
        permissions=current_user.get("permissions", []),
        created_at=current_user.get("created_at", datetime.utcnow()),
        updated_at=current_user.get("updated_at", datetime.utcnow())
    )


@router.put("/me", response_model=UserProfileResponse)
async def update_current_user_profile(
    updates: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_repo: UserProfileRepository = Depends(get_user_repository)
):
    """Update current user profile."""
    user_id = current_user["id"]
    logger.info("Update user profile", user_id=user_id, updates=list(updates.keys()))
    
    # Filter allowed updates (users can't change their own role)
    allowed_fields = ["email"]  # Add other allowed fields
    filtered_updates = {k: v for k, v in updates.items() if k in allowed_fields}
    
    if not filtered_updates:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No valid fields to update"
        )
    
    # TODO: Implement actual profile update
    # For now, return the current user data
    return UserProfileResponse(
        id=current_user["id"],
        email=current_user.get("email", ""),
        role=current_user.get("role", "viewer"),
        permissions=current_user.get("permissions", []),
        created_at=current_user.get("created_at", datetime.utcnow()),
        updated_at=datetime.utcnow()
    )


@router.post("/logout")
async def logout(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """Logout user (invalidate token)."""
    logger.info("User logout", user_id=current_user["id"])
    
    # TODO: Implement token blacklisting if needed
    # For JWT tokens, logout is typically handled client-side
    # by removing the token from storage
    
    return {"message": "Successfully logged out"}


@router.post("/refresh")
async def refresh_token(
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """Refresh access token."""
    logger.info("Token refresh", user_id=current_user["id"])
    
    # Create new access token
    token_data = {"sub": current_user["id"], "email": current_user.get("email")}
    access_token = create_access_token(token_data)
    
    return TokenResponse(
        access_token=access_token,
        expires_in=settings.jwt_access_token_expire_minutes * 60,
        user=current_user
    )


@router.get("/verify")
async def verify_token(
    current_user: Dict[str, Any] = Depends(get_current_active_user)
):
    """Verify if current token is valid."""
    return {
        "valid": True,
        "user_id": current_user["id"],
        "role": current_user.get("role", "viewer")
    }
