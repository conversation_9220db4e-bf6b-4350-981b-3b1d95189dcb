"""
Domain and DNS management with Cloudflare integration.

Handles automated DNS record creation, domain routing, and redirect
management for deployed trend sites.
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any
from datetime import datetime
import structlog

logger = structlog.get_logger()


class CloudflareClient:
    """Client for Cloudflare API operations."""
    
    def __init__(self, api_token: str, zone_id: str):
        """
        Initialize Cloudflare client.
        
        Args:
            api_token: Cloudflare API token
            zone_id: Cloudflare zone ID
        """
        self.api_token = api_token
        self.zone_id = zone_id
        self.base_url = "https://api.cloudflare.com/client/v4"
        self.session = None
        
        self.logger = structlog.get_logger().bind(component="cloudflare_client")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self.session is None:
            headers = {
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json"
            }
            self.session = aiohttp.ClientSession(headers=headers)
        return self.session
    
    async def test_connection(self) -> bool:
        """Test connection to Cloudflare API."""
        try:
            session = await self._get_session()
            async with session.get(f"{self.base_url}/zones/{self.zone_id}") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success"):
                        self.logger.info("Cloudflare connection successful", zone=data["result"]["name"])
                        return True
                return False
        except Exception as e:
            self.logger.error("Cloudflare connection test failed", error=str(e))
            return False
    
    async def create_dns_record(
        self,
        record_type: str,
        name: str,
        content: str,
        ttl: int = 300,
        proxied: bool = True
    ) -> Dict[str, Any]:
        """
        Create a DNS record.
        
        Args:
            record_type: Record type (A, CNAME, etc.)
            name: Record name
            content: Record content
            ttl: TTL in seconds
            proxied: Whether to proxy through Cloudflare
            
        Returns:
            DNS record creation result
        """
        session = await self._get_session()
        
        payload = {
            "type": record_type,
            "name": name,
            "content": content,
            "ttl": ttl,
            "proxied": proxied
        }
        
        try:
            async with session.post(
                f"{self.base_url}/zones/{self.zone_id}/dns_records",
                json=payload
            ) as response:
                data = await response.json()
                
                if data.get("success"):
                    record = data["result"]
                    self.logger.info(
                        "DNS record created",
                        record_type=record_type,
                        name=name,
                        record_id=record["id"]
                    )
                    return {
                        "success": True,
                        "record_id": record["id"],
                        "name": record["name"],
                        "content": record["content"],
                        "type": record["type"]
                    }
                else:
                    errors = data.get("errors", [])
                    error_msg = "; ".join([err.get("message", "Unknown error") for err in errors])
                    self.logger.error("DNS record creation failed", error=error_msg)
                    return {
                        "success": False,
                        "error": error_msg
                    }
        
        except Exception as e:
            self.logger.error("DNS record creation failed", error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_dns_record(self, name: str) -> Optional[Dict[str, Any]]:
        """Get DNS record by name."""
        session = await self._get_session()
        
        try:
            params = {"name": name}
            async with session.get(
                f"{self.base_url}/zones/{self.zone_id}/dns_records",
                params=params
            ) as response:
                data = await response.json()
                
                if data.get("success") and data["result"]:
                    return data["result"][0]
                return None
        
        except Exception as e:
            self.logger.error("Failed to get DNS record", name=name, error=str(e))
            return None
    
    async def update_dns_record(
        self,
        record_id: str,
        content: str,
        ttl: int = 300,
        proxied: bool = True
    ) -> Dict[str, Any]:
        """Update existing DNS record."""
        session = await self._get_session()
        
        # Get existing record first
        try:
            async with session.get(
                f"{self.base_url}/zones/{self.zone_id}/dns_records/{record_id}"
            ) as response:
                data = await response.json()
                if not data.get("success"):
                    return {"success": False, "error": "Record not found"}
                
                existing_record = data["result"]
        except Exception as e:
            return {"success": False, "error": str(e)}
        
        # Update record
        payload = {
            "type": existing_record["type"],
            "name": existing_record["name"],
            "content": content,
            "ttl": ttl,
            "proxied": proxied
        }
        
        try:
            async with session.put(
                f"{self.base_url}/zones/{self.zone_id}/dns_records/{record_id}",
                json=payload
            ) as response:
                data = await response.json()
                
                if data.get("success"):
                    self.logger.info("DNS record updated", record_id=record_id)
                    return {
                        "success": True,
                        "record_id": record_id,
                        "content": content
                    }
                else:
                    errors = data.get("errors", [])
                    error_msg = "; ".join([err.get("message", "Unknown error") for err in errors])
                    return {"success": False, "error": error_msg}
        
        except Exception as e:
            self.logger.error("DNS record update failed", record_id=record_id, error=str(e))
            return {"success": False, "error": str(e)}
    
    async def delete_dns_record(self, record_id: str) -> bool:
        """Delete DNS record."""
        session = await self._get_session()
        
        try:
            async with session.delete(
                f"{self.base_url}/zones/{self.zone_id}/dns_records/{record_id}"
            ) as response:
                data = await response.json()
                
                if data.get("success"):
                    self.logger.info("DNS record deleted", record_id=record_id)
                    return True
                return False
        
        except Exception as e:
            self.logger.error("DNS record deletion failed", record_id=record_id, error=str(e))
            return False
    
    async def create_page_rule(
        self,
        url_pattern: str,
        actions: Dict[str, Any],
        priority: int = 1
    ) -> Dict[str, Any]:
        """Create Cloudflare page rule for redirects."""
        session = await self._get_session()
        
        payload = {
            "targets": [{"target": "url", "constraint": {"operator": "matches", "value": url_pattern}}],
            "actions": [{"id": key, "value": value} for key, value in actions.items()],
            "priority": priority,
            "status": "active"
        }
        
        try:
            async with session.post(
                f"{self.base_url}/zones/{self.zone_id}/pagerules",
                json=payload
            ) as response:
                data = await response.json()
                
                if data.get("success"):
                    rule = data["result"]
                    self.logger.info("Page rule created", rule_id=rule["id"], pattern=url_pattern)
                    return {
                        "success": True,
                        "rule_id": rule["id"],
                        "pattern": url_pattern
                    }
                else:
                    errors = data.get("errors", [])
                    error_msg = "; ".join([err.get("message", "Unknown error") for err in errors])
                    return {"success": False, "error": error_msg}
        
        except Exception as e:
            self.logger.error("Page rule creation failed", error=str(e))
            return {"success": False, "error": str(e)}
    
    async def close(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None


class DomainManager:
    """Manages domain operations and DNS configuration."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize domain manager.
        
        Args:
            config: Domain management configuration
        """
        self.config = config or {}
        
        # Cloudflare configuration
        self.cloudflare_client = None
        if self.config.get("cloudflare_api_token") and self.config.get("zone_id"):
            self.cloudflare_client = CloudflareClient(
                api_token=self.config["cloudflare_api_token"],
                zone_id=self.config["zone_id"]
            )
        
        # Domain configuration
        self.base_domain = self.config.get("base_domain", "trends.example.com")
        self.subdomain_pattern = self.config.get("subdomain_pattern", "{slug}")
        self.enable_redirects = self.config.get("enable_redirects", True)
        
        self.logger = structlog.get_logger().bind(component="domain_manager")
    
    async def generate_domain_name(self, trend_slug: str) -> str:
        """Generate domain name for a trend."""
        subdomain = self.subdomain_pattern.format(slug=trend_slug)
        return f"{subdomain}.{self.base_domain}"
    
    async def create_cname_record(
        self,
        domain: str,
        target: str,
        proxied: bool = True
    ) -> Dict[str, Any]:
        """
        Create CNAME record for custom domain.
        
        Args:
            domain: Domain name to create record for
            target: Target domain to point to
            proxied: Whether to proxy through Cloudflare
            
        Returns:
            DNS record creation result
        """
        if not self.cloudflare_client:
            return {
                "success": False,
                "error": "Cloudflare not configured"
            }
        
        try:
            # Check if record already exists
            existing_record = await self.cloudflare_client.get_dns_record(domain)
            
            if existing_record:
                # Update existing record
                result = await self.cloudflare_client.update_dns_record(
                    record_id=existing_record["id"],
                    content=target,
                    proxied=proxied
                )
                result["action"] = "updated"
            else:
                # Create new record
                result = await self.cloudflare_client.create_dns_record(
                    record_type="CNAME",
                    name=domain,
                    content=target,
                    proxied=proxied
                )
                result["action"] = "created"
            
            if result["success"]:
                self.logger.info(
                    "CNAME record configured",
                    domain=domain,
                    target=target,
                    action=result["action"]
                )
            
            return result
        
        except Exception as e:
            self.logger.error("CNAME record creation failed", domain=domain, error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def setup_trend_domain(
        self,
        trend_slug: str,
        target_domain: str,
        custom_domain: str = None
    ) -> Dict[str, Any]:
        """
        Setup complete domain configuration for a trend.
        
        Args:
            trend_slug: Trend slug for subdomain generation
            target_domain: Target domain from Coolify
            custom_domain: Optional custom domain
            
        Returns:
            Domain setup results
        """
        results = {
            "primary_domain": None,
            "custom_domain": None,
            "redirects": []
        }
        
        # Setup primary subdomain
        primary_domain = await self.generate_domain_name(trend_slug)
        primary_result = await self.create_cname_record(primary_domain, target_domain)
        
        results["primary_domain"] = {
            "domain": primary_domain,
            "result": primary_result
        }
        
        # Setup custom domain if provided
        if custom_domain:
            custom_result = await self.create_cname_record(custom_domain, target_domain)
            results["custom_domain"] = {
                "domain": custom_domain,
                "result": custom_result
            }
        
        # Setup redirects if enabled
        if self.enable_redirects:
            redirect_results = await self._setup_redirects(trend_slug, primary_domain, custom_domain)
            results["redirects"] = redirect_results
        
        self.logger.info(
            "Domain setup completed",
            trend_slug=trend_slug,
            primary_domain=primary_domain,
            custom_domain=custom_domain
        )
        
        return results
    
    async def _setup_redirects(
        self,
        trend_slug: str,
        primary_domain: str,
        custom_domain: str = None
    ) -> List[Dict[str, Any]]:
        """Setup redirect rules for SEO and user experience."""
        redirects = []
        
        if not self.cloudflare_client:
            return redirects
        
        # Redirect variations to primary domain
        redirect_patterns = [
            f"www.{primary_domain}/*",
            f"{trend_slug}.{self.base_domain.replace('trends.', '')}/*",  # Alternative subdomain
        ]
        
        for pattern in redirect_patterns:
            try:
                redirect_result = await self.cloudflare_client.create_page_rule(
                    url_pattern=pattern,
                    actions={
                        "forwarding_url": {
                            "url": f"https://{primary_domain}/$1",
                            "status_code": 301
                        }
                    }
                )
                redirects.append({
                    "pattern": pattern,
                    "target": primary_domain,
                    "result": redirect_result
                })
            except Exception as e:
                self.logger.warning("Failed to create redirect", pattern=pattern, error=str(e))
        
        return redirects
    
    async def delete_trend_domain(self, trend_slug: str) -> Dict[str, Any]:
        """Delete domain configuration for a trend."""
        if not self.cloudflare_client:
            return {"success": False, "error": "Cloudflare not configured"}
        
        domain = await self.generate_domain_name(trend_slug)
        
        try:
            # Get DNS record
            record = await self.cloudflare_client.get_dns_record(domain)
            
            if record:
                # Delete DNS record
                deleted = await self.cloudflare_client.delete_dns_record(record["id"])
                
                if deleted:
                    self.logger.info("Domain deleted", domain=domain)
                    return {
                        "success": True,
                        "domain": domain,
                        "message": "Domain deleted successfully"
                    }
            
            return {
                "success": False,
                "error": "Domain not found"
            }
        
        except Exception as e:
            self.logger.error("Domain deletion failed", domain=domain, error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_domain_status(self, domain: str) -> Dict[str, Any]:
        """Get domain configuration status."""
        if not self.cloudflare_client:
            return {"configured": False, "error": "Cloudflare not configured"}
        
        try:
            record = await self.cloudflare_client.get_dns_record(domain)
            
            if record:
                return {
                    "configured": True,
                    "record_type": record["type"],
                    "content": record["content"],
                    "proxied": record.get("proxied", False),
                    "ttl": record.get("ttl", 300)
                }
            else:
                return {
                    "configured": False,
                    "message": "Domain not found"
                }
        
        except Exception as e:
            self.logger.error("Failed to get domain status", domain=domain, error=str(e))
            return {
                "configured": False,
                "error": str(e)
            }
    
    async def list_managed_domains(self) -> List[Dict[str, Any]]:
        """List all managed domains."""
        if not self.cloudflare_client:
            return []
        
        try:
            session = await self.cloudflare_client._get_session()
            
            # Get all DNS records for the zone
            async with session.get(
                f"{self.cloudflare_client.base_url}/zones/{self.cloudflare_client.zone_id}/dns_records"
            ) as response:
                data = await response.json()
                
                if data.get("success"):
                    # Filter records that match our base domain pattern
                    managed_records = []
                    for record in data["result"]:
                        if self.base_domain in record["name"]:
                            managed_records.append({
                                "id": record["id"],
                                "name": record["name"],
                                "type": record["type"],
                                "content": record["content"],
                                "proxied": record.get("proxied", False)
                            })
                    
                    return managed_records
                
                return []
        
        except Exception as e:
            self.logger.error("Failed to list managed domains", error=str(e))
            return []
    
    async def close(self):
        """Close connections."""
        if self.cloudflare_client:
            await self.cloudflare_client.close()
