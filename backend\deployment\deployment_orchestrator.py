"""
Main deployment orchestrator.

Coordinates the complete deployment process including Git operations,
Coolify deployment, DNS management, and deployment tracking.
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import structlog

from .coolify_client import CoolifyClient
from .git_operations import GitOperations
from .deployment_tracker import DeploymentTracker
from .domain_manager import DomainManager
from database.models.deployment_model import DeploymentRepository
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository

logger = structlog.get_logger()


class DeploymentOrchestrator:
    """Main orchestrator for deployment operations."""
    
    def __init__(
        self,
        deployment_repository: DeploymentRepository,
        trend_repository: TrendRepository,
        content_repository: ContentRepository,
        config: Dict[str, Any] = None
    ):
        """
        Initialize deployment orchestrator.
        
        Args:
            deployment_repository: Database repository for deployments
            trend_repository: Database repository for trends
            content_repository: Database repository for content
            config: Configuration dictionary
        """
        self.deployment_repo = deployment_repository
        self.trend_repo = trend_repository
        self.content_repo = content_repository
        self.config = config or self._get_default_config()
        
        # Initialize components
        self.coolify_client = CoolifyClient(
            api_url=self.config["coolify"]["api_url"],
            api_token=self.config["coolify"]["api_token"]
        )
        
        self.git_ops = GitOperations(self.config.get("git", {}))
        
        self.deployment_tracker = DeploymentTracker(
            deployment_repository=self.deployment_repo,
            coolify_client=self.coolify_client,
            config=self.config.get("tracking", {})
        )
        
        self.domain_manager = DomainManager(
            config=self.config.get("dns", {})
        )
        
        # Statistics tracking
        self.stats = {
            "deployments_started": 0,
            "deployments_completed": 0,
            "deployments_failed": 0,
            "errors": []
        }
        
        self.logger = structlog.get_logger().bind(component="deployment_orchestrator")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            "coolify": {
                "api_url": "https://coolify.yourdomain.com",
                "api_token": "",
                "build_pack": "static"
            },
            "git": {
                "git_user": "Trend Platform Deploy",
                "git_email": "<EMAIL>",
                "default_branch": "main"
            },
            "dns": {
                "cloudflare_api_token": "",
                "zone_id": "",
                "base_domain": "trends.yourdomain.com"
            },
            "tracking": {
                "poll_interval": 30,
                "max_deployment_time": 1800
            }
        }
    
    async def deploy_trend_site(
        self,
        trend_id: str,
        force_redeploy: bool = False,
        custom_domain: str = None
    ) -> Dict[str, Any]:
        """
        Deploy a complete trend site.
        
        Args:
            trend_id: ID of the trend to deploy
            force_redeploy: Whether to force redeployment
            custom_domain: Custom domain for the site
            
        Returns:
            Deployment results and metadata
        """
        start_time = datetime.utcnow()
        
        self.logger.info("Starting trend site deployment", trend_id=trend_id)
        
        try:
            # Get trend and content data
            trend_data = await self.trend_repo.get_by_id(trend_id)
            if not trend_data:
                raise ValueError(f"Trend not found: {trend_id}")
            
            content_data = await self.content_repo.get_by_trend_id(trend_id)
            if not content_data:
                raise ValueError(f"No content found for trend: {trend_id}")
            
            # Check if deployment already exists
            existing_deployment = await self.deployment_repo.get_by_trend_id(trend_id)
            if existing_deployment and not force_redeploy:
                if existing_deployment["status"] in ["success", "building"]:
                    self.logger.info("Deployment already exists", trend_id=trend_id)
                    return {
                        "success": True,
                        "action": "existing",
                        "deployment_id": existing_deployment["id"],
                        "deploy_url": existing_deployment.get("deploy_url")
                    }
            
            # Create deployment record
            deployment_id = await self._create_deployment_record(trend_data, content_data, custom_domain)
            
            # Step 1: Prepare Git repository
            git_result = await self._prepare_git_repository(trend_data, content_data)
            if not git_result["success"]:
                raise Exception(f"Git preparation failed: {git_result['error']}")
            
            # Step 2: Create/Update Coolify application
            app_result = await self._setup_coolify_application(
                trend_data, git_result, custom_domain
            )
            if not app_result["success"]:
                raise Exception(f"Coolify setup failed: {app_result['error']}")
            
            # Step 3: Deploy to Coolify
            deploy_result = await self._deploy_to_coolify(
                app_result["application_id"], git_result["commit_sha"]
            )
            if not deploy_result["success"]:
                raise Exception(f"Coolify deployment failed: {deploy_result['error']}")
            
            # Step 4: Start deployment tracking
            await self.deployment_tracker.start_deployment_tracking(
                deployment_id=deployment_id,
                coolify_deployment_id=deploy_result["deployment_id"],
                trend_id=trend_id
            )
            
            # Step 5: Setup DNS (if custom domain)
            dns_result = None
            if custom_domain:
                dns_result = await self._setup_dns(custom_domain, app_result["default_domain"])
            
            # Update deployment record with results
            await self._update_deployment_record(
                deployment_id, app_result, deploy_result, dns_result
            )
            
            self.stats["deployments_started"] += 1
            
            result = {
                "success": True,
                "deployment_id": deployment_id,
                "trend_id": trend_id,
                "application_id": app_result["application_id"],
                "coolify_deployment_id": deploy_result["deployment_id"],
                "git_commit_sha": git_result["commit_sha"],
                "deploy_url": app_result.get("default_domain"),
                "custom_domain": custom_domain,
                "dns_configured": dns_result["success"] if dns_result else False,
                "deployment_time": (datetime.utcnow() - start_time).total_seconds()
            }
            
            self.logger.info(
                "Trend site deployment initiated",
                trend_id=trend_id,
                deployment_id=deployment_id,
                deploy_url=result["deploy_url"]
            )
            
            return result
        
        except Exception as e:
            error_msg = f"Deployment failed for trend {trend_id}: {str(e)}"
            self.logger.error("Deployment failed", trend_id=trend_id, error=str(e))
            self.stats["errors"].append(error_msg)
            self.stats["deployments_failed"] += 1
            
            return {
                "success": False,
                "error": str(e),
                "trend_id": trend_id,
                "deployment_time": (datetime.utcnow() - start_time).total_seconds()
            }
    
    async def _create_deployment_record(
        self,
        trend_data: Dict[str, Any],
        content_data: Dict[str, Any],
        custom_domain: str = None
    ) -> str:
        """Create initial deployment record."""
        
        deployment_data = {
            "trend_id": trend_data["id"],
            "content_id": content_data["id"],
            "status": "pending",
            "progress": 0,
            "custom_domain": custom_domain,
            "created_at": datetime.utcnow()
        }
        
        deployment_id = await self.deployment_repo.create(deployment_data)
        
        self.logger.info("Deployment record created", deployment_id=deployment_id, trend_id=trend_data["id"])
        
        return deployment_id
    
    async def _prepare_git_repository(
        self,
        trend_data: Dict[str, Any],
        content_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare Git repository for deployment."""
        
        # Generate repository name
        repo_name = f"trend-{trend_data['slug']}"
        
        # Get site path (this would come from the site builder)
        site_path = f"/tmp/generated_sites/{trend_data['slug']}"
        
        # Generate remote URL (this would be configured)
        remote_url = f"https://github.com/your-org/{repo_name}.git"
        
        # Create/update repository
        git_result = await self.git_ops.create_deployment_repository(
            site_path=site_path,
            repo_name=repo_name,
            remote_url=remote_url
        )
        
        return git_result
    
    async def _setup_coolify_application(
        self,
        trend_data: Dict[str, Any],
        git_result: Dict[str, Any],
        custom_domain: str = None
    ) -> Dict[str, Any]:
        """Setup Coolify application."""
        
        app_name = f"trend-{trend_data['slug']}"
        
        try:
            # Check if application already exists
            existing_apps = await self.coolify_client.list_applications()
            existing_app = next((app for app in existing_apps if app["name"] == app_name), None)
            
            if existing_app:
                # Update existing application
                app_id = existing_app["id"]
                
                # Update domains if custom domain provided
                if custom_domain:
                    domains = [custom_domain]
                    await self.coolify_client.update_application_domains(app_id, domains)
                
                result = {
                    "success": True,
                    "application_id": app_id,
                    "action": "updated",
                    "default_domain": existing_app.get("default_domain"),
                    "custom_domains": [custom_domain] if custom_domain else []
                }
            else:
                # Create new application
                domains = [custom_domain] if custom_domain else []
                
                app_data = await self.coolify_client.create_application(
                    name=app_name,
                    git_repository=git_result["remote_url"],
                    git_branch=git_result["branch"],
                    build_pack=self.config["coolify"]["build_pack"],
                    domains=domains
                )
                
                result = {
                    "success": True,
                    "application_id": app_data["id"],
                    "action": "created",
                    "default_domain": app_data.get("default_domain"),
                    "custom_domains": domains
                }
            
            self.logger.info(
                "Coolify application setup completed",
                app_name=app_name,
                app_id=result["application_id"],
                action=result["action"]
            )
            
            return result
        
        except Exception as e:
            self.logger.error("Coolify application setup failed", app_name=app_name, error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _deploy_to_coolify(
        self,
        application_id: str,
        commit_sha: str = None
    ) -> Dict[str, Any]:
        """Deploy application to Coolify."""
        
        try:
            deployment = await self.coolify_client.deploy_application(
                application_id=application_id,
                force_rebuild=True,
                commit_sha=commit_sha
            )
            
            return {
                "success": True,
                "deployment_id": deployment.id,
                "status": deployment.status,
                "commit_sha": deployment.commit_sha
            }
        
        except Exception as e:
            self.logger.error("Coolify deployment failed", app_id=application_id, error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _setup_dns(
        self,
        custom_domain: str,
        target_domain: str
    ) -> Dict[str, Any]:
        """Setup DNS records for custom domain."""
        
        try:
            dns_result = await self.domain_manager.create_cname_record(
                domain=custom_domain,
                target=target_domain
            )
            
            return dns_result
        
        except Exception as e:
            self.logger.error("DNS setup failed", domain=custom_domain, error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _update_deployment_record(
        self,
        deployment_id: str,
        app_result: Dict[str, Any],
        deploy_result: Dict[str, Any],
        dns_result: Dict[str, Any] = None
    ):
        """Update deployment record with results."""
        
        update_data = {
            "coolify_app_uuid": app_result.get("application_id"),
            "coolify_deployment_uuid": deploy_result.get("deployment_id"),
            "deploy_url": app_result.get("default_domain"),
            "git_commit_sha": deploy_result.get("commit_sha"),
            "updated_at": datetime.utcnow()
        }
        
        if dns_result:
            update_data["dns_configured"] = dns_result.get("success", False)
        
        await self.deployment_repo.update(deployment_id, update_data)
    
    async def get_deployment_status(self, deployment_id: str) -> Dict[str, Any]:
        """Get comprehensive deployment status."""
        
        deployment = await self.deployment_repo.get_by_id(deployment_id)
        if not deployment:
            return {"error": "Deployment not found"}
        
        # Get Coolify status if available
        coolify_status = None
        if deployment.get("coolify_deployment_uuid"):
            try:
                coolify_deployment = await self.coolify_client.get_deployment_status(
                    deployment["coolify_deployment_uuid"]
                )
                coolify_status = {
                    "status": coolify_deployment.status,
                    "deploy_url": coolify_deployment.deploy_url,
                    "finished_at": coolify_deployment.finished_at
                }
            except:
                pass
        
        return {
            "deployment": deployment,
            "coolify_status": coolify_status,
            "is_monitored": deployment["id"] in self.deployment_tracker.monitoring_tasks
        }
    
    async def batch_deploy_trends(
        self,
        trend_ids: List[str],
        max_concurrent: int = 3
    ) -> Dict[str, Any]:
        """Deploy multiple trends concurrently."""
        
        self.logger.info("Starting batch deployment", trend_count=len(trend_ids))
        
        # Create semaphore to limit concurrency
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def deploy_with_semaphore(trend_id: str):
            async with semaphore:
                return await self.deploy_trend_site(trend_id)
        
        # Execute all deployments
        tasks = [deploy_with_semaphore(trend_id) for trend_id in trend_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        successful = 0
        failed = 0
        errors = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed += 1
                errors.append(f"Trend {trend_ids[i]}: {str(result)}")
            elif result.get("success"):
                successful += 1
            else:
                failed += 1
                errors.append(f"Trend {trend_ids[i]}: {result.get('error', 'Unknown error')}")
        
        batch_result = {
            "total_trends": len(trend_ids),
            "successful": successful,
            "failed": failed,
            "success_rate": successful / len(trend_ids) if trend_ids else 0,
            "errors": errors,
            "results": [r for r in results if not isinstance(r, Exception)]
        }
        
        self.logger.info(
            "Batch deployment completed",
            total=len(trend_ids),
            successful=successful,
            failed=failed
        )
        
        return batch_result
    
    async def get_deployment_metrics(self) -> Dict[str, Any]:
        """Get deployment metrics and statistics."""
        
        # Get tracker metrics
        tracker_metrics = await self.deployment_tracker.get_deployment_metrics()
        
        # Combine with orchestrator stats
        combined_metrics = {
            "orchestrator_stats": dict(self.stats),
            "tracker_metrics": tracker_metrics,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return combined_metrics
    
    async def close(self):
        """Close all connections and cleanup resources."""
        await self.coolify_client.close()
        await self.deployment_tracker.stop_all_monitoring()
        await self.domain_manager.close()
        
        self.logger.info("Deployment orchestrator closed")
