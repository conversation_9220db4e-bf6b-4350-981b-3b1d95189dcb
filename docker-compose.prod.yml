version: '3.8'

services:
  # Main API Service
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: trend-platform-api
    restart: unless-stopped
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - COOLIFY_API_URL=${COOLIFY_API_URL}
      - COOLIFY_API_TOKEN=${COOLIFY_API_TOKEN}
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      - CLOUDFLARE_ZONE_ID=${CLOUDFLARE_ZONE_ID}
      - BASE_DOMAIN=${BASE_DOMAIN}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - CORS_ORIGINS=${CORS_ORIGINS}
    ports:
      - "8000:8000"
    volumes:
      - ./logs:/app/logs
      - ./generated_sites:/app/generated_sites
    depends_on:
      - redis
    networks:
      - trend-platform
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Worker for Scraping
  celery-scraper:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: trend-platform-celery-scraper
    restart: unless-stopped
    command: celery -A scraper.tasks worker --loglevel=info --queues=scraper --concurrency=4
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./generated_sites:/app/generated_sites
    depends_on:
      - redis
    networks:
      - trend-platform
    healthcheck:
      test: ["CMD", "celery", "-A", "scraper.tasks", "inspect", "ping"]
      interval: 60s
      timeout: 10s
      retries: 3

  # Celery Worker for Content Generation
  celery-generator:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: trend-platform-celery-generator
    restart: unless-stopped
    command: celery -A generator.tasks worker --loglevel=info --queues=generator --concurrency=2
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./logs:/app/logs
      - ./generated_sites:/app/generated_sites
    depends_on:
      - redis
    networks:
      - trend-platform
    healthcheck:
      test: ["CMD", "celery", "-A", "generator.tasks", "inspect", "ping"]
      interval: 60s
      timeout: 10s
      retries: 3

  # Celery Worker for Deployment
  celery-deployment:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: trend-platform-celery-deployment
    restart: unless-stopped
    command: celery -A deployment.tasks worker --loglevel=info --queues=deployment --concurrency=3
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - COOLIFY_API_URL=${COOLIFY_API_URL}
      - COOLIFY_API_TOKEN=${COOLIFY_API_TOKEN}
      - CLOUDFLARE_API_TOKEN=${CLOUDFLARE_API_TOKEN}
      - CLOUDFLARE_ZONE_ID=${CLOUDFLARE_ZONE_ID}
      - BASE_DOMAIN=${BASE_DOMAIN}
      - GIT_USER=${GIT_USER}
      - GIT_EMAIL=${GIT_EMAIL}
    volumes:
      - ./logs:/app/logs
      - ./generated_sites:/app/generated_sites
      - ~/.ssh:/root/.ssh:ro  # For Git operations
    depends_on:
      - redis
    networks:
      - trend-platform
    healthcheck:
      test: ["CMD", "celery", "-A", "deployment.tasks", "inspect", "ping"]
      interval: 60s
      timeout: 10s
      retries: 3

  # Celery Beat Scheduler
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: trend-platform-celery-beat
    restart: unless-stopped
    command: celery -A scraper.tasks beat --loglevel=info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - redis
    networks:
      - trend-platform

  # Redis for Celery
  redis:
    image: redis:7-alpine
    container_name: trend-platform-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - trend-platform
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Flower for Celery Monitoring
  flower:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    container_name: trend-platform-flower
    restart: unless-stopped
    command: celery -A scraper.tasks flower --port=5555
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=${REDIS_URL}
      - FLOWER_BASIC_AUTH=${FLOWER_BASIC_AUTH}
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - trend-platform

  # Frontend Dashboard
  dashboard:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
      args:
        - VITE_API_URL=${VITE_API_URL}
        - VITE_APP_TITLE=${VITE_APP_TITLE}
    container_name: trend-platform-dashboard
    restart: unless-stopped
    ports:
      - "3000:80"
    networks:
      - trend-platform
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: trend-platform-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/sites-available:/etc/nginx/sites-available:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - api
      - dashboard
    networks:
      - trend-platform
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Prometheus for Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: trend-platform-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - trend-platform

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: trend-platform-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - trend-platform

volumes:
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  trend-platform:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
