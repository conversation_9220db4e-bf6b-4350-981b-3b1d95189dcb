"""
Git operations for deployment automation.

Handles Git repository management, pushing code to remote repositories,
and managing deployment branches for Coolify integration.
"""

import asyncio
import subprocess
import tempfile
import shutil
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import structlog

logger = structlog.get_logger()


class GitOperations:
    """Git operations manager for deployment automation."""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize Git operations.
        
        Args:
            config: Git configuration including credentials and settings
        """
        self.config = config or {}
        self.git_user = self.config.get("git_user", "Trend Platform")
        self.git_email = self.config.get("git_email", "<EMAIL>")
        self.default_branch = self.config.get("default_branch", "main")
        
        self.logger = structlog.get_logger().bind(component="git_operations")
    
    async def create_deployment_repository(
        self,
        site_path: str,
        repo_name: str,
        remote_url: str,
        branch: str = None
    ) -> Dict[str, Any]:
        """
        Create and push a deployment repository.
        
        Args:
            site_path: Path to the generated site
            repo_name: Repository name
            remote_url: Remote repository URL
            branch: Branch name (defaults to main)
            
        Returns:
            Repository information and status
        """
        branch = branch or self.default_branch
        site_path = Path(site_path)
        
        self.logger.info(
            "Creating deployment repository",
            repo_name=repo_name,
            site_path=str(site_path),
            branch=branch
        )
        
        try:
            # Initialize repository if not exists
            if not (site_path / ".git").exists():
                await self._run_git_command(["init"], cwd=site_path)
                await self._configure_git_user(site_path)
            
            # Add remote if not exists
            remotes = await self._get_remotes(site_path)
            if "origin" not in remotes:
                await self._run_git_command(["remote", "add", "origin", remote_url], cwd=site_path)
            else:
                # Update remote URL
                await self._run_git_command(["remote", "set-url", "origin", remote_url], cwd=site_path)
            
            # Create .gitignore if not exists
            gitignore_path = site_path / ".gitignore"
            if not gitignore_path.exists():
                await self._create_gitignore(gitignore_path)
            
            # Stage all files
            await self._run_git_command(["add", "."], cwd=site_path)
            
            # Check if there are changes to commit
            has_changes = await self._has_uncommitted_changes(site_path)
            
            if has_changes:
                # Commit changes
                commit_message = f"Deploy {repo_name} - {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}"
                await self._run_git_command(["commit", "-m", commit_message], cwd=site_path)
                
                # Push to remote
                await self._run_git_command(["push", "-u", "origin", branch], cwd=site_path)
                
                # Get commit SHA
                commit_sha = await self._get_current_commit_sha(site_path)
                
                result = {
                    "success": True,
                    "repository": repo_name,
                    "branch": branch,
                    "commit_sha": commit_sha,
                    "remote_url": remote_url,
                    "has_changes": True,
                    "message": "Repository created and pushed successfully"
                }
            else:
                # No changes to commit
                commit_sha = await self._get_current_commit_sha(site_path)
                
                result = {
                    "success": True,
                    "repository": repo_name,
                    "branch": branch,
                    "commit_sha": commit_sha,
                    "remote_url": remote_url,
                    "has_changes": False,
                    "message": "No changes to commit"
                }
            
            self.logger.info(
                "Repository operation completed",
                repo_name=repo_name,
                commit_sha=result["commit_sha"],
                has_changes=result["has_changes"]
            )
            
            return result
        
        except Exception as e:
            self.logger.error("Failed to create deployment repository", repo_name=repo_name, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "repository": repo_name
            }
    
    async def update_repository(
        self,
        site_path: str,
        commit_message: str = None,
        branch: str = None
    ) -> Dict[str, Any]:
        """
        Update existing repository with new changes.
        
        Args:
            site_path: Path to the site repository
            commit_message: Custom commit message
            branch: Branch to push to
            
        Returns:
            Update status and information
        """
        site_path = Path(site_path)
        branch = branch or self.default_branch
        
        if not commit_message:
            commit_message = f"Update site - {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}"
        
        try:
            # Check if repository exists
            if not (site_path / ".git").exists():
                raise ValueError("Not a git repository")
            
            # Stage all changes
            await self._run_git_command(["add", "."], cwd=site_path)
            
            # Check for changes
            has_changes = await self._has_uncommitted_changes(site_path)
            
            if has_changes:
                # Commit changes
                await self._run_git_command(["commit", "-m", commit_message], cwd=site_path)
                
                # Push changes
                await self._run_git_command(["push", "origin", branch], cwd=site_path)
                
                # Get new commit SHA
                commit_sha = await self._get_current_commit_sha(site_path)
                
                result = {
                    "success": True,
                    "commit_sha": commit_sha,
                    "branch": branch,
                    "has_changes": True,
                    "message": "Repository updated successfully"
                }
            else:
                commit_sha = await self._get_current_commit_sha(site_path)
                
                result = {
                    "success": True,
                    "commit_sha": commit_sha,
                    "branch": branch,
                    "has_changes": False,
                    "message": "No changes to commit"
                }
            
            self.logger.info(
                "Repository updated",
                site_path=str(site_path),
                commit_sha=result["commit_sha"],
                has_changes=result["has_changes"]
            )
            
            return result
        
        except Exception as e:
            self.logger.error("Failed to update repository", site_path=str(site_path), error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def clone_repository(
        self,
        remote_url: str,
        local_path: str,
        branch: str = None
    ) -> Dict[str, Any]:
        """
        Clone a repository to local path.
        
        Args:
            remote_url: Remote repository URL
            local_path: Local path to clone to
            branch: Specific branch to clone
            
        Returns:
            Clone operation status
        """
        local_path = Path(local_path)
        branch = branch or self.default_branch
        
        try:
            # Remove existing directory if it exists
            if local_path.exists():
                shutil.rmtree(local_path)
            
            # Clone repository
            clone_args = ["clone", "--branch", branch, remote_url, str(local_path)]
            await self._run_git_command(clone_args)
            
            # Configure git user
            await self._configure_git_user(local_path)
            
            # Get commit SHA
            commit_sha = await self._get_current_commit_sha(local_path)
            
            result = {
                "success": True,
                "local_path": str(local_path),
                "branch": branch,
                "commit_sha": commit_sha,
                "message": "Repository cloned successfully"
            }
            
            self.logger.info(
                "Repository cloned",
                remote_url=remote_url,
                local_path=str(local_path),
                commit_sha=commit_sha
            )
            
            return result
        
        except Exception as e:
            self.logger.error("Failed to clone repository", remote_url=remote_url, error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _run_git_command(
        self,
        args: List[str],
        cwd: Path = None,
        check: bool = True
    ) -> subprocess.CompletedProcess:
        """Run git command."""
        cmd = ["git"] + args
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=cwd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if check and process.returncode != 0:
            error_msg = stderr.decode() if stderr else "Git command failed"
            raise subprocess.CalledProcessError(
                process.returncode, cmd, stdout, stderr
            )
        
        return subprocess.CompletedProcess(
            cmd, process.returncode, stdout, stderr
        )
    
    async def _configure_git_user(self, repo_path: Path):
        """Configure git user for repository."""
        await self._run_git_command(["config", "user.name", self.git_user], cwd=repo_path)
        await self._run_git_command(["config", "user.email", self.git_email], cwd=repo_path)
    
    async def _get_remotes(self, repo_path: Path) -> List[str]:
        """Get list of remote names."""
        try:
            result = await self._run_git_command(["remote"], cwd=repo_path)
            remotes = result.stdout.decode().strip().split('\n')
            return [r for r in remotes if r]
        except:
            return []
    
    async def _has_uncommitted_changes(self, repo_path: Path) -> bool:
        """Check if repository has uncommitted changes."""
        try:
            result = await self._run_git_command(["diff", "--cached", "--quiet"], cwd=repo_path, check=False)
            return result.returncode != 0
        except:
            return False
    
    async def _get_current_commit_sha(self, repo_path: Path) -> str:
        """Get current commit SHA."""
        try:
            result = await self._run_git_command(["rev-parse", "HEAD"], cwd=repo_path)
            return result.stdout.decode().strip()
        except:
            return ""
    
    async def _create_gitignore(self, gitignore_path: Path):
        """Create basic .gitignore file."""
        gitignore_content = """
# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
.next/
out/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
tmp/
temp/
        """.strip()
        
        with open(gitignore_path, 'w') as f:
            f.write(gitignore_content)
    
    async def get_repository_info(self, repo_path: str) -> Dict[str, Any]:
        """Get repository information."""
        repo_path = Path(repo_path)
        
        try:
            if not (repo_path / ".git").exists():
                return {"is_git_repo": False}
            
            # Get current branch
            branch_result = await self._run_git_command(["branch", "--show-current"], cwd=repo_path)
            current_branch = branch_result.stdout.decode().strip()
            
            # Get commit SHA
            commit_sha = await self._get_current_commit_sha(repo_path)
            
            # Get remote URL
            try:
                remote_result = await self._run_git_command(["remote", "get-url", "origin"], cwd=repo_path)
                remote_url = remote_result.stdout.decode().strip()
            except:
                remote_url = None
            
            # Check for uncommitted changes
            has_changes = await self._has_uncommitted_changes(repo_path)
            
            return {
                "is_git_repo": True,
                "current_branch": current_branch,
                "commit_sha": commit_sha,
                "remote_url": remote_url,
                "has_uncommitted_changes": has_changes
            }
        
        except Exception as e:
            self.logger.error("Failed to get repository info", repo_path=str(repo_path), error=str(e))
            return {
                "is_git_repo": False,
                "error": str(e)
            }
