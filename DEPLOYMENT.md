# Trend Platform Deployment Guide

This guide covers the complete deployment process for the Trend Platform on OCI Ampere A1 infrastructure with Coolify orchestration.

## 🏗️ Architecture Overview

The Trend Platform uses a microservices architecture with the following components:

- **API Service**: FastAPI backend with REST endpoints
- **Celery Workers**: Background task processing (scraping, generation, deployment)
- **Redis**: Message broker and caching
- **PostgreSQL**: Primary database
- **Nginx**: Reverse proxy and load balancer
- **Dashboard**: React frontend
- **Monitoring**: Prometheus + Grafana

## 🚀 Quick Start

### Prerequisites

1. **OCI Ampere A1 Instance** (minimum 4 OCPU, 24GB RAM)
2. **Docker & Docker Compose** installed
3. **Domain name** with DNS management access
4. **Coolify instance** running
5. **Cloudflare account** (for DNS management)
6. **OpenAI API key** (for content generation)

### 1. Clone and Setup

```bash
git clone <repository-url>
cd trend-platform
cp .env.prod.example .env.prod
```

### 2. Configure Environment

Edit `.env.prod` with your settings:

```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/trend_platform

# API Keys
OPENAI_API_KEY=sk-your-openai-key
COOLIFY_API_TOKEN=your-coolify-token
CLOUDFLARE_API_TOKEN=your-cloudflare-token

# Domains
DOMAIN=yourdomain.com
BASE_DOMAIN=trends.yourdomain.com
CLOUDFLARE_ZONE_ID=your-zone-id

# Security
JWT_SECRET_KEY=your-super-secret-key
```

### 3. Deploy

```bash
chmod +x deploy.sh
./deploy.sh
```

The deployment script will:
- Check system requirements
- Setup SSL certificates
- Build and start all services
- Configure monitoring
- Perform health checks

## 📋 Detailed Configuration

### Environment Variables

#### Core Settings
- `ENVIRONMENT`: production/staging/development
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `JWT_SECRET_KEY`: Secret for JWT token signing

#### API Keys
- `OPENAI_API_KEY`: OpenAI API key for content generation
- `COOLIFY_API_TOKEN`: Coolify API token for deployments
- `CLOUDFLARE_API_TOKEN`: Cloudflare API token for DNS

#### Domain Configuration
- `DOMAIN`: Main domain for the platform
- `BASE_DOMAIN`: Base domain for trend sites (e.g., trends.yourdomain.com)
- `CLOUDFLARE_ZONE_ID`: Cloudflare zone ID for DNS management

#### Security
- `CORS_ORIGINS`: Allowed CORS origins
- `FLOWER_BASIC_AUTH`: Basic auth for Flower monitoring (user:pass)
- `GRAFANA_ADMIN_PASSWORD`: Grafana admin password

### Service Configuration

#### API Service
- **Port**: 8000
- **Workers**: 4 (configurable)
- **Health Check**: `/health` endpoint
- **Documentation**: `/docs` (Swagger UI)

#### Celery Workers
- **Scraper Queue**: Handles trend scraping tasks
- **Generator Queue**: Handles content generation tasks
- **Deployment Queue**: Handles deployment tasks
- **Beat Scheduler**: Manages periodic tasks

#### Monitoring
- **Prometheus**: Metrics collection (port 9090)
- **Grafana**: Visualization dashboard (port 3001)
- **Flower**: Celery monitoring (port 5555)

## 🔧 Operations

### Starting Services

```bash
# Start all services
docker-compose -f docker-compose.prod.yml up -d

# Start specific service
docker-compose -f docker-compose.prod.yml up -d api

# View logs
docker-compose -f docker-compose.prod.yml logs -f api
```

### Updating Deployment

```bash
# Update with latest changes
./deploy.sh --update

# Manual update
git pull
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d
```

### Health Monitoring

```bash
# Check service health
./deploy.sh --health

# View service status
docker-compose -f docker-compose.prod.yml ps

# Check individual service logs
./deploy.sh --logs api
./deploy.sh --logs celery-scraper
```

### Backup and Restore

```bash
# Create backup
./deploy.sh --backup

# Backups are stored in ./backups/ directory
# Restore from backup (manual process)
```

## 🔍 Monitoring and Debugging

### Service URLs

- **Dashboard**: http://localhost:3000
- **API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Flower**: http://localhost:5555
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001

### Log Locations

- **Application Logs**: `./logs/`
- **Nginx Logs**: `./logs/nginx/`
- **Container Logs**: `docker-compose logs [service]`

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database connectivity
docker-compose exec api python -c "from database.connection import test_connection; test_connection()"

# Check database logs
docker logs trend-platform-postgres
```

#### 2. Celery Worker Issues
```bash
# Check worker status
docker-compose exec celery-scraper celery -A scraper.tasks inspect ping

# Restart workers
docker-compose restart celery-scraper celery-generator celery-deployment
```

#### 3. SSL Certificate Issues
```bash
# Renew certificates
certbot renew

# Copy new certificates
cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem ./ssl/cert.pem
cp /etc/letsencrypt/live/$DOMAIN/privkey.pem ./ssl/key.pem

# Restart nginx
docker-compose restart nginx
```

## 🔐 Security Considerations

### SSL/TLS
- Automatic SSL certificate generation with Let's Encrypt
- HSTS headers enabled
- Secure cipher suites configured

### Authentication
- JWT-based authentication
- Secure password hashing with bcrypt
- Rate limiting on API endpoints

### Network Security
- Services isolated in Docker network
- Nginx reverse proxy with security headers
- Firewall rules (configure on OCI)

### Secrets Management
- Environment variables for sensitive data
- No secrets in code or logs
- Regular secret rotation recommended

## 📊 Performance Optimization

### Resource Allocation
- **API**: 2 CPU cores, 4GB RAM
- **Celery Workers**: 1 CPU core, 2GB RAM each
- **Database**: 1 CPU core, 4GB RAM
- **Redis**: 0.5 CPU core, 1GB RAM

### Scaling
- Horizontal scaling: Add more Celery workers
- Vertical scaling: Increase container resources
- Database scaling: Read replicas for heavy queries

### Caching
- Redis for session and API response caching
- Nginx static file caching
- CDN integration (Cloudflare)

## 🔄 CI/CD Integration

### GitHub Actions (Example)

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Deploy to server
        run: |
          ssh user@server 'cd /path/to/trend-platform && ./deploy.sh --update'
```

### Coolify Integration
- Automatic deployments from Git repositories
- Environment variable management
- Health check monitoring
- Rollback capabilities

## 📈 Monitoring and Alerting

### Metrics Collected
- API response times and error rates
- Celery task execution metrics
- Database performance metrics
- System resource utilization

### Grafana Dashboards
- Application performance overview
- Celery task monitoring
- Infrastructure metrics
- Business metrics (trends processed, content generated)

### Alerting Rules
- High error rates
- Service downtime
- Resource exhaustion
- Failed deployments

## 🆘 Troubleshooting

### Service Won't Start
1. Check environment variables
2. Verify database connectivity
3. Check port conflicts
4. Review service logs

### Performance Issues
1. Monitor resource usage
2. Check database query performance
3. Review Celery task queues
4. Analyze API response times

### Deployment Failures
1. Check Coolify API connectivity
2. Verify Cloudflare DNS settings
3. Review deployment logs
4. Check Git repository access

## 📞 Support

For deployment issues:
1. Check this documentation
2. Review service logs
3. Check monitoring dashboards
4. Contact system administrator

## 🔄 Updates and Maintenance

### Regular Maintenance
- Weekly: Review logs and metrics
- Monthly: Update dependencies
- Quarterly: Security audit
- Annually: Infrastructure review

### Update Process
1. Test in staging environment
2. Create backup
3. Deploy during maintenance window
4. Verify all services
5. Monitor for issues
