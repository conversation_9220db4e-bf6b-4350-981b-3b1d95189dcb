"""
Rate limiting utilities for web scraping.

Provides rate limiting functionality to prevent overwhelming target servers
and avoid getting blocked or rate limited.
"""

import asyncio
import time
from typing import Dict, Optional
from collections import deque
import structlog

logger = structlog.get_logger()


class RateLimiter:
    """Async rate limiter for controlling request frequency."""
    
    def __init__(
        self,
        requests_per_minute: int = 60,
        requests_per_hour: int = None,
        burst_limit: int = 5
    ):
        """
        Initialize rate limiter.
        
        Args:
            requests_per_minute: Maximum requests per minute
            requests_per_hour: Maximum requests per hour (optional)
            burst_limit: Maximum burst requests allowed
        """
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour or requests_per_minute * 60
        self.burst_limit = burst_limit
        
        # Track request timestamps
        self.minute_requests = deque()
        self.hour_requests = deque()
        self.burst_requests = deque()
        
        # Locks for thread safety
        self._lock = asyncio.Lock()
        
        self.logger = structlog.get_logger().bind(component="rate_limiter")
    
    async def acquire(self) -> None:
        """
        Acquire permission to make a request.
        Blocks until request is allowed under rate limits.
        """
        async with self._lock:
            current_time = time.time()
            
            # Clean old requests
            self._cleanup_old_requests(current_time)
            
            # Check if we need to wait
            wait_time = self._calculate_wait_time(current_time)
            
            if wait_time > 0:
                self.logger.info("Rate limit reached, waiting", wait_seconds=wait_time)
                await asyncio.sleep(wait_time)
                current_time = time.time()
                self._cleanup_old_requests(current_time)
            
            # Record the request
            self._record_request(current_time)
            
            self.logger.debug(
                "Request acquired",
                minute_count=len(self.minute_requests),
                hour_count=len(self.hour_requests),
                burst_count=len(self.burst_requests)
            )
    
    def _cleanup_old_requests(self, current_time: float) -> None:
        """Remove old request timestamps that are outside the time windows."""
        
        # Clean minute window (60 seconds)
        minute_cutoff = current_time - 60
        while self.minute_requests and self.minute_requests[0] < minute_cutoff:
            self.minute_requests.popleft()
        
        # Clean hour window (3600 seconds)
        hour_cutoff = current_time - 3600
        while self.hour_requests and self.hour_requests[0] < hour_cutoff:
            self.hour_requests.popleft()
        
        # Clean burst window (10 seconds)
        burst_cutoff = current_time - 10
        while self.burst_requests and self.burst_requests[0] < burst_cutoff:
            self.burst_requests.popleft()
    
    def _calculate_wait_time(self, current_time: float) -> float:
        """Calculate how long to wait before allowing the next request."""
        wait_times = []
        
        # Check minute limit
        if len(self.minute_requests) >= self.requests_per_minute:
            oldest_minute_request = self.minute_requests[0]
            wait_times.append(oldest_minute_request + 60 - current_time)
        
        # Check hour limit
        if len(self.hour_requests) >= self.requests_per_hour:
            oldest_hour_request = self.hour_requests[0]
            wait_times.append(oldest_hour_request + 3600 - current_time)
        
        # Check burst limit
        if len(self.burst_requests) >= self.burst_limit:
            oldest_burst_request = self.burst_requests[0]
            wait_times.append(oldest_burst_request + 10 - current_time)
        
        return max(wait_times) if wait_times else 0
    
    def _record_request(self, current_time: float) -> None:
        """Record a new request timestamp."""
        self.minute_requests.append(current_time)
        self.hour_requests.append(current_time)
        self.burst_requests.append(current_time)
    
    def get_stats(self) -> Dict[str, int]:
        """Get current rate limiting statistics."""
        current_time = time.time()
        self._cleanup_old_requests(current_time)
        
        return {
            "requests_last_minute": len(self.minute_requests),
            "requests_last_hour": len(self.hour_requests),
            "requests_last_burst": len(self.burst_requests),
            "minute_limit": self.requests_per_minute,
            "hour_limit": self.requests_per_hour,
            "burst_limit": self.burst_limit
        }
    
    def reset(self) -> None:
        """Reset all rate limiting counters."""
        self.minute_requests.clear()
        self.hour_requests.clear()
        self.burst_requests.clear()
        self.logger.info("Rate limiter reset")


class AdaptiveRateLimiter(RateLimiter):
    """Rate limiter that adapts based on response codes and errors."""
    
    def __init__(
        self,
        initial_requests_per_minute: int = 60,
        min_requests_per_minute: int = 10,
        max_requests_per_minute: int = 120,
        **kwargs
    ):
        """
        Initialize adaptive rate limiter.
        
        Args:
            initial_requests_per_minute: Starting rate limit
            min_requests_per_minute: Minimum rate limit
            max_requests_per_minute: Maximum rate limit
        """
        super().__init__(initial_requests_per_minute, **kwargs)
        
        self.initial_rpm = initial_requests_per_minute
        self.min_rpm = min_requests_per_minute
        self.max_rpm = max_requests_per_minute
        
        # Adaptation tracking
        self.success_count = 0
        self.error_count = 0
        self.last_adaptation = time.time()
        self.adaptation_interval = 300  # 5 minutes
    
    async def record_response(self, status_code: int, error: Optional[Exception] = None) -> None:
        """
        Record response for adaptive rate limiting.
        
        Args:
            status_code: HTTP status code
            error: Exception if request failed
        """
        async with self._lock:
            current_time = time.time()
            
            if error or status_code >= 400:
                self.error_count += 1
                
                # Immediate slowdown for rate limit errors
                if status_code == 429 or (error and "rate limit" in str(error).lower()):
                    await self._decrease_rate_limit()
            else:
                self.success_count += 1
            
            # Periodic adaptation
            if current_time - self.last_adaptation > self.adaptation_interval:
                await self._adapt_rate_limit()
                self.last_adaptation = current_time
    
    async def _adapt_rate_limit(self) -> None:
        """Adapt rate limit based on success/error ratio."""
        total_requests = self.success_count + self.error_count
        
        if total_requests == 0:
            return
        
        error_rate = self.error_count / total_requests
        
        if error_rate > 0.1:  # More than 10% errors
            await self._decrease_rate_limit()
        elif error_rate < 0.02:  # Less than 2% errors
            await self._increase_rate_limit()
        
        # Reset counters
        self.success_count = 0
        self.error_count = 0
    
    async def _decrease_rate_limit(self) -> None:
        """Decrease rate limit due to errors."""
        new_rpm = max(self.min_rpm, int(self.requests_per_minute * 0.8))
        
        if new_rpm != self.requests_per_minute:
            self.logger.info(
                "Decreasing rate limit",
                old_rpm=self.requests_per_minute,
                new_rpm=new_rpm
            )
            self.requests_per_minute = new_rpm
    
    async def _increase_rate_limit(self) -> None:
        """Increase rate limit due to low error rate."""
        new_rpm = min(self.max_rpm, int(self.requests_per_minute * 1.1))
        
        if new_rpm != self.requests_per_minute:
            self.logger.info(
                "Increasing rate limit",
                old_rpm=self.requests_per_minute,
                new_rpm=new_rpm
            )
            self.requests_per_minute = new_rpm
