# Hybrid Google Trends Content Platform

A comprehensive content automation platform that scrapes trending topics, generates AI-powered content, and deploys static sites automatically.

## 🚀 Features

- **Trend Scraping**: Automated scraping from Google Trends and trends24.in
- **AI Content Generation**: OpenAI-compatible text and image generation
- **Static Site Deployment**: Automated deployment via Coolify
- **DNS Management**: Cloudflare integration for domain management
- **Analytics Dashboard**: Real-time monitoring and reporting
- **Role-based Access**: Admin, Editor, and Viewer roles
- **Automated Cleanup**: Content lifecycle management

## 🏗️ Architecture

### Technology Stack
- **Backend**: Python (FastAPI), Celery, Redis
- **Frontend**: Next.js, TypeScript, Tailwind CSS
- **Database**: Supabase (PostgreSQL) with Row Level Security
- **Infrastructure**: OCI Ampere A1, Coolify, Cloudflare
- **AI Services**: OpenAI-compatible endpoints

### System Components
1. **Trend Scraper Module** - Data collection and processing
2. **Site Generator Module** - AI-powered content creation
3. **Database & Storage Module** - Data persistence and management
4. **Dashboard & Control Module** - User interface and controls
5. **Coolify Deploy Orchestrator** - Deployment automation
6. **DNS & Redirect Layer** - Domain and routing management
7. **House-keeping Module** - Cleanup and maintenance
8. **Monitoring & Observability** - Logging and metrics
9. **Security & Compliance** - Authentication and authorization
10. **Infrastructure as Code** - Automated provisioning

## 🛠️ Development Setup

### Prerequisites
- Python 3.11+
- Node.js 18+
- Docker and Docker Compose
- PostgreSQL (or use Docker)
- Redis (or use Docker)

### Quick Start with Docker

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd trendSite2
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Start the development environment**
   ```bash
   docker-compose up -d
   ```

4. **Access the applications**
   - Backend API: http://localhost:8000
   - Frontend Dashboard: http://localhost:3000
   - API Documentation: http://localhost:8000/docs

### Manual Setup

#### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload
```

#### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

## 📊 Database Schema

The platform uses PostgreSQL with the following main tables:
- `trends` - Scraped trend data
- `content` - Generated content
- `deployments` - Deployment tracking
- `dns_records` - DNS management
- `analytics` - Event tracking
- `user_profiles` - User management

## 🔧 Configuration

### Environment Variables

Key configuration options:

```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/trend_platform
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key

# AI Services
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# Deployment
COOLIFY_API_URL=https://deploy.yourdomain.com
COOLIFY_API_TOKEN=your_coolify_api_token

# DNS
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ZONE_ID=your_zone_id
```

See `.env.example` for complete configuration options.

## 🚀 Deployment

### Production Deployment

1. **Infrastructure Setup**
   ```bash
   # Provision OCI Ampere A1 VM
   # Install Docker and Docker Compose
   # Configure SSL certificates
   ```

2. **Application Deployment**
   ```bash
   # Clone repository on server
   git clone <repository-url>
   cd trendSite2
   
   # Set production environment variables
   cp .env.example .env
   # Edit .env with production values
   
   # Deploy with Docker Compose
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
   ```

3. **Database Migration**
   ```bash
   # Run database migrations
   docker-compose exec backend python -m database.migrations.migration_manager
   ```

## 📈 Usage

### Dashboard Access

1. **Login**: Access the dashboard at your domain
2. **Trend Management**: View and approve pending trends
3. **Content Generation**: Trigger AI content creation
4. **Deployment**: Deploy approved content to live sites
5. **Analytics**: Monitor performance and engagement

### API Usage

The platform provides a RESTful API for programmatic access:

```bash
# Get trends
curl -X GET "http://localhost:8000/api/v1/trends" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Approve trends
curl -X POST "http://localhost:8000/api/v1/trends/approve" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"trend_ids": ["trend-id-1", "trend-id-2"]}'

# Generate content
curl -X POST "http://localhost:8000/api/v1/content/generate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"trend_id": "trend-id"}'
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest tests/ -v --cov=app
```

### Frontend Tests
```bash
cd frontend
npm test
npm run test:coverage
```

### Integration Tests
```bash
# Run full test suite
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 📝 API Documentation

- **Interactive Docs**: http://localhost:8000/docs (Swagger UI)
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI Spec**: http://localhost:8000/openapi.json

## 🔒 Security

- JWT-based authentication
- Role-based access control (RBAC)
- Row Level Security (RLS) in database
- API rate limiting
- Input validation and sanitization
- HTTPS enforcement in production

## 📊 Monitoring

- Structured logging with JSON format
- Prometheus metrics collection
- Grafana dashboards
- Real-time error tracking
- Performance monitoring

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation in `/docs`
- Review API documentation at `/docs` endpoint
- Create an issue for bugs or feature requests

## 🗺️ Roadmap

- [ ] Advanced AI content customization
- [ ] Multi-language support
- [ ] Enhanced analytics and reporting
- [ ] Mobile app for dashboard
- [ ] Integration with additional trend sources
- [ ] Advanced SEO optimization
- [ ] A/B testing for content variations
