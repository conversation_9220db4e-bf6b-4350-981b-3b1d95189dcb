"""
Error handling and retry utilities for web scraping.

Provides robust error handling, retry mechanisms, and circuit breaker
patterns for reliable web scraping operations.
"""

import asyncio
import random
from typing import Callable, Any, Optional, List, Type
from dataclasses import dataclass
from enum import Enum
import time
import structlog

logger = structlog.get_logger()


class RetryStrategy(Enum):
    """Retry strategy enumeration."""
    FIXED = "fixed"
    EXPONENTIAL = "exponential"
    LINEAR = "linear"
    RANDOM = "random"


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    backoff_multiplier: float = 2.0
    jitter: bool = True
    retryable_exceptions: List[Type[Exception]] = None
    
    def __post_init__(self):
        if self.retryable_exceptions is None:
            self.retryable_exceptions = [
                ConnectionError,
                TimeoutError,
                OSError,
                asyncio.TimeoutError
            ]


class CircuitBreakerState(Enum):
    """Circuit breaker state enumeration."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, rejecting requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreaker:
    """Circuit breaker pattern implementation for fault tolerance."""
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: Type[Exception] = Exception
    ):
        """
        Initialize circuit breaker.
        
        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time to wait before attempting recovery
            expected_exception: Exception type that triggers circuit breaker
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitBreakerState.CLOSED
        
        self.logger = structlog.get_logger().bind(component="circuit_breaker")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: If circuit is open or function fails
        """
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitBreakerState.HALF_OPEN
                self.logger.info("Circuit breaker transitioning to half-open")
            else:
                raise Exception("Circuit breaker is open")
        
        try:
            result = await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            self._on_success()
            return result
        
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        return (
            self.last_failure_time is not None and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
    
    def _on_success(self):
        """Handle successful function execution."""
        self.failure_count = 0
        self.state = CircuitBreakerState.CLOSED
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.logger.info("Circuit breaker reset to closed")
    
    def _on_failure(self):
        """Handle failed function execution."""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = CircuitBreakerState.OPEN
            self.logger.warning(
                "Circuit breaker opened",
                failure_count=self.failure_count,
                threshold=self.failure_threshold
            )


class RetryHandler:
    """Retry handler with configurable strategies and circuit breaker."""
    
    def __init__(self, config: RetryConfig = None, circuit_breaker: CircuitBreaker = None):
        """
        Initialize retry handler.
        
        Args:
            config: Retry configuration
            circuit_breaker: Optional circuit breaker for fault tolerance
        """
        self.config = config or RetryConfig()
        self.circuit_breaker = circuit_breaker
        self.logger = structlog.get_logger().bind(component="retry_handler")
    
    async def execute(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with retry logic.
        
        Args:
            func: Function to execute
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Function result
            
        Raises:
            Exception: If all retry attempts fail
        """
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                # Use circuit breaker if available
                if self.circuit_breaker:
                    return await self.circuit_breaker.call(func, *args, **kwargs)
                else:
                    return await func(*args, **kwargs) if asyncio.iscoroutinefunction(func) else func(*args, **kwargs)
            
            except Exception as e:
                last_exception = e
                
                # Check if exception is retryable
                if not self._is_retryable_exception(e):
                    self.logger.error("Non-retryable exception", error=str(e), attempt=attempt + 1)
                    raise e
                
                # Don't sleep on the last attempt
                if attempt < self.config.max_attempts - 1:
                    delay = self._calculate_delay(attempt)
                    self.logger.warning(
                        "Retrying after failure",
                        error=str(e),
                        attempt=attempt + 1,
                        max_attempts=self.config.max_attempts,
                        delay=delay
                    )
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(
                        "All retry attempts failed",
                        error=str(e),
                        attempts=self.config.max_attempts
                    )
        
        # All attempts failed
        raise last_exception
    
    def _is_retryable_exception(self, exception: Exception) -> bool:
        """Check if exception is retryable."""
        return any(
            isinstance(exception, exc_type)
            for exc_type in self.config.retryable_exceptions
        )
    
    def _calculate_delay(self, attempt: int) -> float:
        """Calculate delay for retry attempt."""
        if self.config.strategy == RetryStrategy.FIXED:
            delay = self.config.base_delay
        
        elif self.config.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.config.base_delay * (self.config.backoff_multiplier ** attempt)
        
        elif self.config.strategy == RetryStrategy.LINEAR:
            delay = self.config.base_delay * (attempt + 1)
        
        elif self.config.strategy == RetryStrategy.RANDOM:
            delay = random.uniform(self.config.base_delay, self.config.max_delay)
        
        else:
            delay = self.config.base_delay
        
        # Apply maximum delay limit
        delay = min(delay, self.config.max_delay)
        
        # Add jitter to prevent thundering herd
        if self.config.jitter:
            jitter_amount = delay * 0.1  # 10% jitter
            delay += random.uniform(-jitter_amount, jitter_amount)
        
        return max(0, delay)


def retry_on_exception(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL,
    retryable_exceptions: List[Type[Exception]] = None
):
    """
    Decorator for adding retry logic to functions.
    
    Args:
        max_attempts: Maximum number of retry attempts
        base_delay: Base delay between retries
        strategy: Retry strategy to use
        retryable_exceptions: List of exceptions that should trigger retry
    """
    def decorator(func: Callable) -> Callable:
        async def async_wrapper(*args, **kwargs):
            config = RetryConfig(
                max_attempts=max_attempts,
                base_delay=base_delay,
                strategy=strategy,
                retryable_exceptions=retryable_exceptions
            )
            handler = RetryHandler(config)
            return await handler.execute(func, *args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            config = RetryConfig(
                max_attempts=max_attempts,
                base_delay=base_delay,
                strategy=strategy,
                retryable_exceptions=retryable_exceptions
            )
            handler = RetryHandler(config)
            return asyncio.run(handler.execute(func, *args, **kwargs))
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class ErrorAggregator:
    """Aggregates and analyzes errors for monitoring and alerting."""
    
    def __init__(self, window_size: int = 100):
        """
        Initialize error aggregator.
        
        Args:
            window_size: Number of recent errors to track
        """
        self.window_size = window_size
        self.errors = []
        self.logger = structlog.get_logger().bind(component="error_aggregator")
    
    def record_error(self, error: Exception, context: dict = None):
        """
        Record an error for analysis.
        
        Args:
            error: Exception that occurred
            context: Additional context information
        """
        error_record = {
            "timestamp": time.time(),
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context or {}
        }
        
        self.errors.append(error_record)
        
        # Keep only recent errors
        if len(self.errors) > self.window_size:
            self.errors = self.errors[-self.window_size:]
        
        self.logger.error("Error recorded", **error_record)
    
    def get_error_stats(self) -> dict:
        """Get error statistics."""
        if not self.errors:
            return {"total_errors": 0}
        
        # Count errors by type
        error_counts = {}
        recent_errors = 0
        current_time = time.time()
        
        for error in self.errors:
            error_type = error["error_type"]
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
            
            # Count errors in last hour
            if current_time - error["timestamp"] < 3600:
                recent_errors += 1
        
        return {
            "total_errors": len(self.errors),
            "recent_errors_1h": recent_errors,
            "error_types": error_counts,
            "error_rate": recent_errors / 60 if recent_errors > 0 else 0  # errors per minute
        }
