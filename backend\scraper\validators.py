"""
Validation utilities for trend data.

Provides comprehensive validation for scraped trend data to ensure
data quality, consistency, and compliance with business rules.
"""

import re
from typing import List, Dict, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import structlog

from .sources.base_scraper import TrendData

logger = structlog.get_logger()


class ValidationSeverity(Enum):
    """Validation severity levels."""
    ERROR = "error"      # Validation failure, reject data
    WARNING = "warning"  # Validation concern, flag for review
    INFO = "info"        # Informational, log but continue


@dataclass
class ValidationResult:
    """Result of a validation check."""
    is_valid: bool
    severity: ValidationSeverity
    message: str
    field: Optional[str] = None
    suggested_fix: Optional[str] = None


class BaseValidator:
    """Base class for trend data validators."""
    
    def __init__(self, name: str, enabled: bool = True):
        self.name = name
        self.enabled = enabled
        self.logger = structlog.get_logger().bind(validator=name)
    
    def validate(self, trend: TrendData) -> List[ValidationResult]:
        """
        Validate trend data.
        
        Args:
            trend: Trend data to validate
            
        Returns:
            List of validation results
        """
        if not self.enabled:
            return []
        
        return self._perform_validation(trend)
    
    def _perform_validation(self, trend: TrendData) -> List[ValidationResult]:
        """
        Perform the actual validation logic.
        
        Args:
            trend: Trend data to validate
            
        Returns:
            List of validation results
        """
        return []


class KeywordValidator(BaseValidator):
    """Validator for trend keywords."""
    
    def __init__(
        self,
        min_length: int = 2,
        max_length: int = 255,
        allowed_chars_pattern: str = r'^[a-zA-Z0-9\s\-_#@&]+$',
        blocked_patterns: List[str] = None,
        required_patterns: List[str] = None
    ):
        super().__init__("keyword_validator")
        self.min_length = min_length
        self.max_length = max_length
        self.allowed_chars_pattern = re.compile(allowed_chars_pattern) if allowed_chars_pattern else None
        self.blocked_patterns = [re.compile(p, re.IGNORECASE) for p in (blocked_patterns or [])]
        self.required_patterns = [re.compile(p, re.IGNORECASE) for p in (required_patterns or [])]
        
        # Common spam/invalid keyword patterns
        self.spam_patterns = [
            re.compile(r'^(.)\1{4,}$'),  # Repeated characters (aaaaa)
            re.compile(r'^\d+$'),        # Only numbers
            re.compile(r'^[^\w\s]+$'),   # Only special characters
            re.compile(r'(buy|sell|cheap|free|click|download)\s*(now|here)', re.IGNORECASE),  # Spam keywords
        ]
    
    def _perform_validation(self, trend: TrendData) -> List[ValidationResult]:
        """Validate keyword field."""
        results = []
        keyword = trend.keyword
        
        # Check if keyword exists
        if not keyword or not keyword.strip():
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message="Keyword is empty or missing",
                field="keyword",
                suggested_fix="Provide a valid keyword"
            ))
            return results
        
        keyword = keyword.strip()
        
        # Check length
        if len(keyword) < self.min_length:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Keyword too short (minimum {self.min_length} characters)",
                field="keyword",
                suggested_fix=f"Use a keyword with at least {self.min_length} characters"
            ))
        
        if len(keyword) > self.max_length:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Keyword too long (maximum {self.max_length} characters)",
                field="keyword",
                suggested_fix=f"Truncate keyword to {self.max_length} characters"
            ))
        
        # Check allowed characters
        if self.allowed_chars_pattern and not self.allowed_chars_pattern.match(keyword):
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.WARNING,
                message="Keyword contains invalid characters",
                field="keyword",
                suggested_fix="Remove or replace invalid characters"
            ))
        
        # Check for blocked patterns
        for pattern in self.blocked_patterns:
            if pattern.search(keyword):
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Keyword matches blocked pattern: {pattern.pattern}",
                    field="keyword",
                    suggested_fix="Use a different keyword"
                ))
        
        # Check for required patterns
        for pattern in self.required_patterns:
            if not pattern.search(keyword):
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.WARNING,
                    message=f"Keyword doesn't match required pattern: {pattern.pattern}",
                    field="keyword",
                    suggested_fix="Modify keyword to match required pattern"
                ))
        
        # Check for spam patterns
        for pattern in self.spam_patterns:
            if pattern.search(keyword):
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.WARNING,
                    message="Keyword appears to be spam or low quality",
                    field="keyword",
                    suggested_fix="Use a more meaningful keyword"
                ))
        
        return results


class MetricsValidator(BaseValidator):
    """Validator for trend metrics (search volume, growth rate, etc.)."""
    
    def __init__(
        self,
        min_search_volume: int = 0,
        max_search_volume: int = 10000000,
        min_growth_rate: float = 0.0,
        max_growth_rate: float = 1000.0,
        min_confidence: float = 0.0,
        max_confidence: float = 1.0
    ):
        super().__init__("metrics_validator")
        self.min_search_volume = min_search_volume
        self.max_search_volume = max_search_volume
        self.min_growth_rate = min_growth_rate
        self.max_growth_rate = max_growth_rate
        self.min_confidence = min_confidence
        self.max_confidence = max_confidence
    
    def _perform_validation(self, trend: TrendData) -> List[ValidationResult]:
        """Validate metrics fields."""
        results = []
        
        # Validate search volume
        if trend.search_volume is not None:
            if trend.search_volume < self.min_search_volume:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.WARNING,
                    message=f"Search volume below minimum ({self.min_search_volume})",
                    field="search_volume",
                    suggested_fix=f"Set search volume to at least {self.min_search_volume}"
                ))
            
            if trend.search_volume > self.max_search_volume:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.WARNING,
                    message=f"Search volume above maximum ({self.max_search_volume})",
                    field="search_volume",
                    suggested_fix=f"Cap search volume at {self.max_search_volume}"
                ))
        
        # Validate growth rate
        if trend.growth_rate is not None:
            if trend.growth_rate < self.min_growth_rate:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.WARNING,
                    message=f"Growth rate below minimum ({self.min_growth_rate})",
                    field="growth_rate",
                    suggested_fix=f"Set growth rate to at least {self.min_growth_rate}"
                ))
            
            if trend.growth_rate > self.max_growth_rate:
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.WARNING,
                    message=f"Growth rate above maximum ({self.max_growth_rate})",
                    field="growth_rate",
                    suggested_fix=f"Cap growth rate at {self.max_growth_rate}"
                ))
        
        # Validate confidence score
        if trend.confidence_score < self.min_confidence:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.WARNING,
                message=f"Confidence score below minimum ({self.min_confidence})",
                field="confidence_score",
                suggested_fix=f"Set confidence score to at least {self.min_confidence}"
            ))
        
        if trend.confidence_score > self.max_confidence:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message=f"Confidence score above maximum ({self.max_confidence})",
                field="confidence_score",
                suggested_fix=f"Cap confidence score at {self.max_confidence}"
            ))
        
        return results


class RegionValidator(BaseValidator):
    """Validator for region/geographic data."""
    
    def __init__(self, valid_regions: List[str] = None):
        super().__init__("region_validator")
        self.valid_regions = set(valid_regions or ["US", "UK", "CA", "AU", "DE", "FR", "JP", "IN"])
    
    def _perform_validation(self, trend: TrendData) -> List[ValidationResult]:
        """Validate region field."""
        results = []
        
        if not trend.region:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.ERROR,
                message="Region is missing",
                field="region",
                suggested_fix="Provide a valid region code"
            ))
        elif trend.region not in self.valid_regions:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.WARNING,
                message=f"Unknown region: {trend.region}",
                field="region",
                suggested_fix=f"Use one of: {', '.join(sorted(self.valid_regions))}"
            ))
        
        return results


class CategoryValidator(BaseValidator):
    """Validator for category data."""
    
    def __init__(self, valid_categories: List[str] = None):
        super().__init__("category_validator")
        self.valid_categories = set(valid_categories or [
            "Technology", "Health", "Entertainment", "Business", "Science",
            "Sports", "Politics", "Education", "Travel", "Food", "Fashion",
            "Gaming", "Music", "Movies", "Social Media", "General"
        ])
    
    def _perform_validation(self, trend: TrendData) -> List[ValidationResult]:
        """Validate category field."""
        results = []
        
        if not trend.category:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.WARNING,
                message="Category is missing",
                field="category",
                suggested_fix="Assign a category or use 'General'"
            ))
        elif trend.category not in self.valid_categories:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.INFO,
                message=f"Unknown category: {trend.category}",
                field="category",
                suggested_fix=f"Use one of: {', '.join(sorted(self.valid_categories))}"
            ))
        
        return results


class TimestampValidator(BaseValidator):
    """Validator for timestamp data."""
    
    def __init__(self, max_age_hours: int = 24):
        super().__init__("timestamp_validator")
        self.max_age_hours = max_age_hours
    
    def _perform_validation(self, trend: TrendData) -> List[ValidationResult]:
        """Validate timestamp field."""
        results = []
        
        if not trend.timestamp:
            results.append(ValidationResult(
                is_valid=False,
                severity=ValidationSeverity.WARNING,
                message="Timestamp is missing",
                field="timestamp",
                suggested_fix="Set timestamp to current time"
            ))
        else:
            # Check if timestamp is too old
            age = datetime.utcnow() - trend.timestamp
            if age > timedelta(hours=self.max_age_hours):
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.WARNING,
                    message=f"Trend data is too old ({age.total_seconds() / 3600:.1f} hours)",
                    field="timestamp",
                    suggested_fix="Use more recent trend data"
                ))
            
            # Check if timestamp is in the future
            if trend.timestamp > datetime.utcnow():
                results.append(ValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message="Timestamp is in the future",
                    field="timestamp",
                    suggested_fix="Set timestamp to current time or earlier"
                ))
        
        return results


class TrendValidator:
    """Main validator that orchestrates all validation checks."""
    
    def __init__(self, validators: List[BaseValidator] = None):
        self.validators = validators or self._get_default_validators()
        self.logger = structlog.get_logger().bind(component="trend_validator")
    
    def _get_default_validators(self) -> List[BaseValidator]:
        """Get default set of validators."""
        return [
            KeywordValidator(),
            MetricsValidator(),
            RegionValidator(),
            CategoryValidator(),
            TimestampValidator()
        ]
    
    def validate_trend(self, trend: TrendData) -> Dict[str, Any]:
        """
        Validate a single trend.
        
        Args:
            trend: Trend data to validate
            
        Returns:
            Validation summary with results and overall status
        """
        all_results = []
        
        for validator in self.validators:
            if validator.enabled:
                results = validator.validate(trend)
                all_results.extend(results)
        
        # Categorize results
        errors = [r for r in all_results if r.severity == ValidationSeverity.ERROR]
        warnings = [r for r in all_results if r.severity == ValidationSeverity.WARNING]
        info = [r for r in all_results if r.severity == ValidationSeverity.INFO]
        
        # Determine overall validity
        is_valid = len(errors) == 0
        
        validation_summary = {
            "is_valid": is_valid,
            "trend_keyword": trend.keyword,
            "total_issues": len(all_results),
            "errors": len(errors),
            "warnings": len(warnings),
            "info": len(info),
            "results": [
                {
                    "severity": r.severity.value,
                    "message": r.message,
                    "field": r.field,
                    "suggested_fix": r.suggested_fix
                }
                for r in all_results
            ]
        }
        
        if not is_valid:
            self.logger.warning(
                "Trend validation failed",
                keyword=trend.keyword,
                errors=len(errors),
                warnings=len(warnings)
            )
        
        return validation_summary
    
    def validate_trends(self, trends: List[TrendData]) -> Dict[str, Any]:
        """
        Validate multiple trends.
        
        Args:
            trends: List of trend data to validate
            
        Returns:
            Batch validation summary
        """
        self.logger.info("Starting batch trend validation", trend_count=len(trends))
        
        valid_trends = []
        invalid_trends = []
        all_issues = []
        
        for trend in trends:
            validation_result = self.validate_trend(trend)
            
            if validation_result["is_valid"]:
                valid_trends.append(trend)
            else:
                invalid_trends.append({
                    "trend": trend,
                    "validation": validation_result
                })
            
            all_issues.extend(validation_result["results"])
        
        # Aggregate statistics
        total_errors = sum(1 for issue in all_issues if issue["severity"] == "error")
        total_warnings = sum(1 for issue in all_issues if issue["severity"] == "warning")
        total_info = sum(1 for issue in all_issues if issue["severity"] == "info")
        
        batch_summary = {
            "total_trends": len(trends),
            "valid_trends": len(valid_trends),
            "invalid_trends": len(invalid_trends),
            "validation_rate": len(valid_trends) / len(trends) if trends else 0,
            "total_issues": len(all_issues),
            "total_errors": total_errors,
            "total_warnings": total_warnings,
            "total_info": total_info,
            "invalid_trend_details": invalid_trends
        }
        
        self.logger.info(
            "Batch validation completed",
            total_trends=len(trends),
            valid_trends=len(valid_trends),
            invalid_trends=len(invalid_trends),
            validation_rate=f"{batch_summary['validation_rate']:.2%}"
        )
        
        return batch_summary
    
    def add_validator(self, validator: BaseValidator):
        """Add a validator to the validation pipeline."""
        self.validators.append(validator)
        self.logger.info("Validator added", validator_name=validator.name)
    
    def remove_validator(self, validator_name: str):
        """Remove a validator from the validation pipeline."""
        self.validators = [v for v in self.validators if v.name != validator_name]
        self.logger.info("Validator removed", validator_name=validator_name)
