-- Initial database schema for Trend Platform
-- This migration creates all the core tables and indexes

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- User profiles table (extends Supabase auth.users)
CREATE TABLE user_profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  role TEXT CHECK (role IN ('admin', 'editor', 'viewer')) DEFAULT 'viewer',
  permissions TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trends table
CREATE TABLE trends (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  keyword TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  status TEXT CHECK (status IN ('pending', 'approved', 'rejected', 'live', 'expired')) DEFAULT 'pending',
  region TEXT NOT NULL,
  category TEXT NOT NULL,
  search_volume INTEGER,
  growth_rate DECIMAL(5,2),
  score DECIMAL(5,2),
  source TEXT NOT NULL,
  raw_data JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expire_at TIMESTAMP WITH TIME ZONE,
  deployed_at TIMESTAMP WITH TIME ZONE,
  ads_enabled BOOLEAN DEFAULT true,
  created_by UUID REFERENCES auth.users(id),
  
  -- Unique constraint to prevent duplicate keywords per region
  CONSTRAINT trends_keyword_region_unique UNIQUE(keyword, region)
);

-- Content table
CREATE TABLE content (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trend_id UUID REFERENCES trends(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  body TEXT NOT NULL,
  meta_tags JSONB DEFAULT '{}',
  hero_image_url TEXT,
  code_snippet TEXT,
  code_language TEXT,
  word_count INTEGER,
  readability_score DECIMAL(3,1),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES auth.users(id)
);

-- Deployments table
CREATE TABLE deployments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trend_id UUID REFERENCES trends(id),
  content_id UUID REFERENCES content(id),
  status TEXT CHECK (status IN ('pending', 'building', 'success', 'failed', 'cancelled')) DEFAULT 'pending',
  coolify_deployment_uuid TEXT,
  build_log TEXT,
  deploy_url TEXT,
  error_message TEXT,
  progress INTEGER DEFAULT 0,
  build_duration INTEGER, -- seconds
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deployed_at TIMESTAMP WITH TIME ZONE,
  triggered_by UUID REFERENCES auth.users(id)
);

-- DNS records table
CREATE TABLE dns_records (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trend_id UUID REFERENCES trends(id),
  subdomain TEXT NOT NULL,
  target_url TEXT NOT NULL,
  cloudflare_record_id TEXT,
  status TEXT CHECK (status IN ('pending', 'active', 'updating', 'deleted', 'error')) DEFAULT 'pending',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  deleted_at TIMESTAMP WITH TIME ZONE
);

-- Analytics table
CREATE TABLE analytics (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  trend_id UUID REFERENCES trends(id),
  event_type TEXT NOT NULL, -- 'page_view', 'redirect', 'error', etc.
  event_data JSONB DEFAULT '{}',
  user_agent TEXT,
  ip_address INET,
  country TEXT,
  referrer TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- System logs table
CREATE TABLE system_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  level TEXT CHECK (level IN ('debug', 'info', 'warning', 'error', 'critical')) NOT NULL,
  module TEXT NOT NULL,
  message TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_trends_status ON trends(status);
CREATE INDEX idx_trends_region ON trends(region);
CREATE INDEX idx_trends_category ON trends(category);
CREATE INDEX idx_trends_created_at ON trends(created_at);
CREATE INDEX idx_trends_score ON trends(score DESC);
CREATE INDEX idx_trends_keyword_trgm ON trends USING gin(keyword gin_trgm_ops);

CREATE INDEX idx_content_trend_id ON content(trend_id);
CREATE INDEX idx_content_created_at ON content(created_at);

CREATE INDEX idx_deployments_trend_id ON deployments(trend_id);
CREATE INDEX idx_deployments_status ON deployments(status);
CREATE INDEX idx_deployments_created_at ON deployments(created_at);

CREATE INDEX idx_dns_records_trend_id ON dns_records(trend_id);
CREATE INDEX idx_dns_records_subdomain ON dns_records(subdomain);
CREATE INDEX idx_dns_records_status ON dns_records(status);

CREATE INDEX idx_analytics_trend_id ON analytics(trend_id);
CREATE INDEX idx_analytics_event_type ON analytics(event_type);
CREATE INDEX idx_analytics_created_at ON analytics(created_at);

CREATE INDEX idx_system_logs_level ON system_logs(level);
CREATE INDEX idx_system_logs_module ON system_logs(module);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);
