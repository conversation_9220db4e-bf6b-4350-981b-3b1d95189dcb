"""
Content model and repository implementation.

Handles all database operations related to generated content including
CRUD operations, content management, and content-specific queries.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, HttpUrl
import asyncpg
from .base_model import BaseRepository
import logging

logger = logging.getLogger(__name__)


class ContentData(BaseModel):
    """Pydantic model for content data validation."""
    id: Optional[str] = None
    trend_id: str
    title: str = Field(..., min_length=1, max_length=500)
    description: Optional[str] = Field(None, max_length=1000)
    body: str = Field(..., min_length=100)
    meta_tags: Dict[str, Any] = Field(default_factory=dict)
    hero_image_url: Optional[str] = None
    code_snippet: Optional[str] = None
    code_language: Optional[str] = None
    word_count: Optional[int] = Field(None, ge=0)
    readability_score: Optional[float] = Field(None, ge=0.0, le=100.0)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    created_by: Optional[str] = None


class ContentRepository(BaseRepository[ContentData]):
    """Repository for content-related database operations."""
    
    def __init__(self, connection_pool: asyncpg.Pool):
        super().__init__(connection_pool, 'content')
    
    async def get_by_trend_id(self, trend_id: str) -> Optional[Dict[str, Any]]:
        """Get content by trend ID."""
        query = "SELECT * FROM content WHERE trend_id = $1 ORDER BY created_at DESC LIMIT 1"
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, trend_id)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Failed to get content by trend_id {trend_id}: {e}")
            raise
    
    async def get_content_with_trend(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Get content with associated trend information."""
        query = """
            SELECT 
                c.*,
                t.keyword,
                t.slug as trend_slug,
                t.status as trend_status,
                t.region,
                t.category
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            WHERE c.id = $1
        """
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, content_id)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Failed to get content with trend {content_id}: {e}")
            raise
    
    async def get_content_by_status(self, trend_status: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get content for trends with specific status."""
        query = """
            SELECT 
                c.*,
                t.keyword,
                t.slug as trend_slug,
                t.status as trend_status,
                t.region,
                t.category
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            WHERE t.status = $1
            ORDER BY c.created_at DESC
            LIMIT $2
        """
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, trend_status, limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get content by status {trend_status}: {e}")
            raise
    
    async def get_recent_content(self, days: int = 7, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recently created content."""
        query = """
            SELECT 
                c.*,
                t.keyword,
                t.slug as trend_slug,
                t.status as trend_status,
                t.region,
                t.category
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            WHERE c.created_at >= NOW() - INTERVAL '%s days'
            ORDER BY c.created_at DESC
            LIMIT $1
        """ % days
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get recent content: {e}")
            raise
    
    async def search_content(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search content by title and body using full-text search."""
        search_query = """
            SELECT 
                c.*,
                t.keyword,
                t.slug as trend_slug,
                t.status as trend_status,
                t.region,
                t.category,
                ts_rank(to_tsvector('english', c.title || ' ' || c.body), plainto_tsquery('english', $1)) as rank
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            WHERE to_tsvector('english', c.title || ' ' || c.body) @@ plainto_tsquery('english', $1)
            ORDER BY rank DESC, c.created_at DESC
            LIMIT $2
        """
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(search_query, query, limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to search content with query '{query}': {e}")
            raise
    
    async def get_content_stats(self) -> Dict[str, Any]:
        """Get content statistics."""
        stats_query = """
            SELECT 
                COUNT(*) as total_content,
                AVG(word_count) as avg_word_count,
                AVG(readability_score) as avg_readability_score,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as recent_content_count
            FROM content
        """
        
        category_query = """
            SELECT 
                t.category,
                COUNT(c.id) as content_count
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            GROUP BY t.category
            ORDER BY content_count DESC
        """
        
        try:
            async with self.pool.acquire() as conn:
                # Get general stats
                stats_row = await conn.fetchrow(stats_query)
                stats = dict(stats_row) if stats_row else {}
                
                # Get category breakdown
                category_rows = await conn.fetch(category_query)
                stats['content_by_category'] = {
                    row['category']: row['content_count'] 
                    for row in category_rows
                }
                
                return stats
        except Exception as e:
            logger.error(f"Failed to get content stats: {e}")
            raise
    
    async def update_word_count(self, content_id: str) -> bool:
        """Update word count for content based on body text."""
        query = """
            UPDATE content 
            SET word_count = array_length(string_to_array(trim(body), ' '), 1),
                updated_at = NOW()
            WHERE id = $1
        """
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.execute(query, content_id)
                updated = result.split()[-1] == '1'
                if updated:
                    logger.info(f"Updated word count for content {content_id}")
                return updated
        except Exception as e:
            logger.error(f"Failed to update word count for content {content_id}: {e}")
            raise
    
    async def get_content_by_category(self, category: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get content filtered by category."""
        query = """
            SELECT 
                c.*,
                t.keyword,
                t.slug as trend_slug,
                t.status as trend_status,
                t.region,
                t.category
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            WHERE t.category = $1
            ORDER BY c.created_at DESC
            LIMIT $2
        """
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, category, limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get content by category {category}: {e}")
            raise
    
    async def get_content_by_region(self, region: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get content filtered by region."""
        query = """
            SELECT 
                c.*,
                t.keyword,
                t.slug as trend_slug,
                t.status as trend_status,
                t.region,
                t.category
            FROM content c
            JOIN trends t ON c.trend_id = t.id
            WHERE t.region = $1
            ORDER BY c.created_at DESC
            LIMIT $2
        """
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, region, limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get content by region {region}: {e}")
            raise
    
    async def delete_content_by_trend(self, trend_id: str) -> bool:
        """Delete all content associated with a trend."""
        query = "DELETE FROM content WHERE trend_id = $1"
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.execute(query, trend_id)
                deleted_count = int(result.split()[-1])
                if deleted_count > 0:
                    logger.info(f"Deleted {deleted_count} content records for trend {trend_id}")
                return deleted_count > 0
        except Exception as e:
            logger.error(f"Failed to delete content for trend {trend_id}: {e}")
            raise
