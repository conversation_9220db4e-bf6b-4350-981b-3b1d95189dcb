"""
Static site builder and Git management.

Handles the generation of complete static sites, Git repository management,
and deployment preparation.
"""

import os
import asyncio
import shutil
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime
import json
import subprocess
import structlog

from .content_templates import Template<PERSON><PERSON><PERSON>
from .asset_manager import AssetManager
from .ai_services import AIContentGenerator

logger = structlog.get_logger()


class GitManager:
    """Git repository management for static sites."""
    
    def __init__(self, repo_path: str, config: Dict[str, Any] = None):
        self.repo_path = Path(repo_path)
        self.config = config or {}
        self.logger = structlog.get_logger().bind(component="git_manager")
    
    async def init_repository(self, remote_url: str = None) -> bool:
        """Initialize Git repository."""
        try:
            # Initialize git repo
            await self._run_git_command(["init"])
            
            # Add remote if provided
            if remote_url:
                await self._run_git_command(["remote", "add", "origin", remote_url])
            
            # Create initial commit
            await self._run_git_command(["add", "."])
            await self._run_git_command(["commit", "-m", "Initial commit"])
            
            self.logger.info("Git repository initialized", repo_path=str(self.repo_path))
            return True
        
        except Exception as e:
            self.logger.error("Failed to initialize Git repository", error=str(e))
            return False
    
    async def commit_changes(self, message: str, files: List[str] = None) -> bool:
        """Commit changes to repository."""
        try:
            # Add specific files or all changes
            if files:
                for file in files:
                    await self._run_git_command(["add", file])
            else:
                await self._run_git_command(["add", "."])
            
            # Check if there are changes to commit
            result = await self._run_git_command(["diff", "--cached", "--quiet"], check=False)
            if result.returncode == 0:
                self.logger.info("No changes to commit")
                return True
            
            # Commit changes
            await self._run_git_command(["commit", "-m", message])
            
            self.logger.info("Changes committed", message=message)
            return True
        
        except Exception as e:
            self.logger.error("Failed to commit changes", error=str(e))
            return False
    
    async def push_changes(self, branch: str = "main") -> bool:
        """Push changes to remote repository."""
        try:
            await self._run_git_command(["push", "origin", branch])
            self.logger.info("Changes pushed to remote", branch=branch)
            return True
        
        except Exception as e:
            self.logger.error("Failed to push changes", error=str(e))
            return False
    
    async def create_branch(self, branch_name: str) -> bool:
        """Create and switch to new branch."""
        try:
            await self._run_git_command(["checkout", "-b", branch_name])
            self.logger.info("Branch created", branch=branch_name)
            return True
        
        except Exception as e:
            self.logger.error("Failed to create branch", branch=branch_name, error=str(e))
            return False
    
    async def _run_git_command(self, args: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """Run git command in repository directory."""
        cmd = ["git"] + args
        
        result = await asyncio.create_subprocess_exec(
            *cmd,
            cwd=self.repo_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await result.communicate()
        
        if check and result.returncode != 0:
            raise subprocess.CalledProcessError(
                result.returncode, cmd, stdout, stderr
            )
        
        return subprocess.CompletedProcess(
            cmd, result.returncode, stdout, stderr
        )


class StaticSiteBuilder:
    """Builds complete static sites from trend data."""
    
    def __init__(
        self,
        output_dir: str,
        template_engine: TemplateEngine = None,
        asset_manager: AssetManager = None,
        config: Dict[str, Any] = None
    ):
        self.output_dir = Path(output_dir)
        self.config = config or {}
        
        # Initialize components
        self.template_engine = template_engine or TemplateEngine()
        self.asset_manager = asset_manager or AssetManager(str(self.output_dir / "assets"))
        self.git_manager = GitManager(str(self.output_dir))
        
        self.logger = structlog.get_logger().bind(component="site_builder")
        
        # Site configuration
        self.site_config = {
            "name": self.config.get("site_name", "Trend Platform"),
            "description": self.config.get("site_description", "Latest trending topics and insights"),
            "base_url": self.config.get("base_url", ""),
            "author": self.config.get("author", "Trend Platform"),
            "language": self.config.get("language", "en"),
            "timezone": self.config.get("timezone", "UTC")
        }
    
    async def build_site(
        self,
        trend_data: Dict[str, Any],
        content_data: Dict[str, Any],
        force_rebuild: bool = False
    ) -> Dict[str, Any]:
        """
        Build complete static site for a trend.
        
        Args:
            trend_data: Trend information
            content_data: Generated content data
            force_rebuild: Whether to force rebuild existing site
            
        Returns:
            Build results and metadata
        """
        site_slug = trend_data.get("slug", "trend-site")
        site_dir = self.output_dir / site_slug
        
        self.logger.info("Starting site build", site_slug=site_slug, trend=trend_data.get("keyword"))
        
        try:
            # Prepare site directory
            if force_rebuild and site_dir.exists():
                shutil.rmtree(site_dir)
            
            site_dir.mkdir(parents=True, exist_ok=True)
            
            # Initialize asset manager for this site
            site_asset_manager = AssetManager(str(site_dir / "assets"))
            
            # Build site structure
            build_result = await self._build_site_structure(
                site_dir, trend_data, content_data, site_asset_manager
            )
            
            # Copy static assets
            await self._copy_static_assets(site_dir)
            
            # Generate additional files
            await self._generate_meta_files(site_dir, trend_data, content_data)
            
            # Initialize Git repository if configured
            if self.config.get("git_enabled", True):
                git_success = await self.git_manager.__class__(str(site_dir)).init_repository(
                    self.config.get("git_remote_url")
                )
                build_result["git_initialized"] = git_success
            
            build_result.update({
                "site_dir": str(site_dir),
                "site_slug": site_slug,
                "build_time": datetime.utcnow().isoformat(),
                "success": True
            })
            
            self.logger.info("Site build completed", site_slug=site_slug, build_time=build_result["build_time"])
            
            return build_result
        
        except Exception as e:
            self.logger.error("Site build failed", site_slug=site_slug, error=str(e))
            return {
                "success": False,
                "error": str(e),
                "site_slug": site_slug,
                "build_time": datetime.utcnow().isoformat()
            }
    
    async def _build_site_structure(
        self,
        site_dir: Path,
        trend_data: Dict[str, Any],
        content_data: Dict[str, Any],
        asset_manager: AssetManager
    ) -> Dict[str, Any]:
        """Build the main site structure and pages."""
        
        # Download and optimize hero image if available
        hero_image_data = None
        if content_data.get("hero_image_url"):
            try:
                hero_image_data = await asset_manager.download_and_optimize_image(
                    content_data["hero_image_url"],
                    f"hero_{trend_data.get('slug', 'image')}"
                )
            except Exception as e:
                self.logger.warning("Failed to process hero image", error=str(e))
        
        # Prepare template context
        context = {
            "site": self.site_config,
            "trend": trend_data,
            "content": content_data,
            "hero_image": hero_image_data,
            "canonical_url": f"{self.site_config['base_url']}/{trend_data.get('slug', '')}",
            "publish_date": datetime.utcnow(),
            "author": self.site_config["author"]
        }
        
        # Generate main article page
        article_html = self.template_engine.render_article_page(
            title=content_data.get("title", trend_data.get("keyword", "Trending Topic")),
            content=content_data.get("content", ""),
            meta_description=content_data.get("meta_description", ""),
            keyword=trend_data.get("keyword", ""),
            category=trend_data.get("category", "General"),
            hero_image_url=hero_image_data["primary"]["relative_path"] if hero_image_data else None,
            code_snippet=content_data.get("code_snippet"),
            **context
        )
        
        # Write article page
        index_path = site_dir / "index.html"
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write(article_html)
        
        # Generate sitemap
        sitemap_urls = [
            {
                "loc": context["canonical_url"],
                "lastmod": datetime.utcnow().strftime("%Y-%m-%d"),
                "changefreq": "weekly",
                "priority": "1.0"
            }
        ]
        
        sitemap_xml = self.template_engine.render_sitemap(sitemap_urls)
        sitemap_path = site_dir / "sitemap.xml"
        with open(sitemap_path, 'w', encoding='utf-8') as f:
            f.write(sitemap_xml)
        
        # Generate robots.txt
        robots_txt = self.template_engine.render_robots_txt(
            sitemap_url=f"{context['canonical_url']}/sitemap.xml"
        )
        robots_path = site_dir / "robots.txt"
        with open(robots_path, 'w', encoding='utf-8') as f:
            f.write(robots_txt)
        
        return {
            "pages_generated": ["index.html", "sitemap.xml", "robots.txt"],
            "hero_image_processed": hero_image_data is not None,
            "assets_processed": True
        }
    
    async def _copy_static_assets(self, site_dir: Path):
        """Copy static CSS, JS, and other assets."""
        # Get static assets directory
        static_dir = Path(__file__).parent / "static"
        
        if static_dir.exists():
            await self.asset_manager.copy_static_assets(str(static_dir))
        
        # Generate basic CSS if not exists
        css_dir = site_dir / "assets" / "css"
        css_dir.mkdir(parents=True, exist_ok=True)
        
        if not (css_dir / "main.css").exists():
            basic_css = self._generate_basic_css()
            with open(css_dir / "main.css", 'w') as f:
                f.write(basic_css)
        
        # Generate basic JS if not exists
        js_dir = site_dir / "assets" / "js"
        js_dir.mkdir(parents=True, exist_ok=True)
        
        if not (js_dir / "main.js").exists():
            basic_js = self._generate_basic_js()
            with open(js_dir / "main.js", 'w') as f:
                f.write(basic_js)
    
    async def _generate_meta_files(
        self,
        site_dir: Path,
        trend_data: Dict[str, Any],
        content_data: Dict[str, Any]
    ):
        """Generate additional meta files."""
        
        # Generate manifest.json for PWA
        manifest = {
            "name": f"{trend_data.get('keyword', 'Trend')} - {self.site_config['name']}",
            "short_name": trend_data.get("keyword", "Trend"),
            "description": content_data.get("meta_description", ""),
            "start_url": "/",
            "display": "standalone",
            "background_color": "#ffffff",
            "theme_color": "#000000",
            "icons": []
        }
        
        manifest_path = site_dir / "manifest.json"
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        # Generate .htaccess for Apache servers
        htaccess_content = """
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
        """.strip()
        
        htaccess_path = site_dir / ".htaccess"
        with open(htaccess_path, 'w') as f:
            f.write(htaccess_content)
    
    def _generate_basic_css(self) -> str:
        """Generate basic CSS styles."""
        return """
/* Basic styles for trend site */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.site-header {
    background: #fff;
    border-bottom: 1px solid #eee;
    padding: 1rem 0;
}

.navbar-brand h1 {
    font-size: 1.5rem;
    color: #333;
}

/* Article */
.article {
    padding: 2rem 0;
}

.article-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.article-meta {
    margin-bottom: 1.5rem;
    color: #666;
}

.category-badge {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    margin-right: 1rem;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 2rem 0;
}

.article-content {
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: 2rem;
}

.article-content h2 {
    margin: 2rem 0 1rem;
    font-size: 1.75rem;
}

.article-content h3 {
    margin: 1.5rem 0 0.75rem;
    font-size: 1.5rem;
}

.article-content p {
    margin-bottom: 1rem;
}

/* Code snippets */
.code-snippet {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

.code-header {
    background: #e9ecef;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.code-snippet pre {
    padding: 1rem;
    margin: 0;
    overflow-x: auto;
}

.copy-button {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.875rem;
}

/* Footer */
.site-footer {
    background: #f8f9fa;
    padding: 2rem 0;
    margin-top: 3rem;
    border-top: 1px solid #eee;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-bottom {
    text-align: center;
    color: #666;
    font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 768px) {
    .article-title {
        font-size: 2rem;
    }
    
    .article-content {
        font-size: 1rem;
    }
    
    .container {
        padding: 0 15px;
    }
}
        """.strip()
    
    def _generate_basic_js(self) -> str:
        """Generate basic JavaScript."""
        return """
// Basic JavaScript for trend site
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
    
    // Add loading states to external links
    document.querySelectorAll('a[href^="http"]').forEach(link => {
        link.addEventListener('click', function() {
            this.style.opacity = '0.7';
        });
    });
});

// Analytics helper functions
function trackEvent(eventName, properties) {
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
    console.log('Event tracked:', eventName, properties);
}

// Track page performance
window.addEventListener('load', function() {
    if ('performance' in window) {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        trackEvent('page_load_time', {
            load_time: loadTime,
            page_type: 'article'
        });
    }
});
        """.strip()
