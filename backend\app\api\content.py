"""
Content API endpoints.

Handles content generation, management, and content-related operations
for approved trends.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field
from datetime import datetime
import structlog

from ..dependencies import (
    get_content_repository,
    get_trend_repository,
    get_current_active_user,
    require_editor,
    get_pagination_params,
    PaginationParams
)
from database.models.content_model import ContentRepository
from database.models.trend_model import TrendRepository

logger = structlog.get_logger()

router = APIRouter(prefix="/content")


class ContentResponse(BaseModel):
    """Content response model."""
    id: str
    trend_id: str
    title: str
    description: Optional[str]
    body: str
    meta_tags: Dict[str, Any]
    hero_image_url: Optional[str]
    code_snippet: Optional[str]
    code_language: Optional[str]
    word_count: Optional[int]
    readability_score: Optional[float]
    created_at: datetime
    updated_at: datetime


class ContentListResponse(BaseModel):
    """Paginated content list response."""
    data: List[ContentResponse]
    pagination: Dict[str, Any]


class ContentGenerationRequest(BaseModel):
    """Content generation request."""
    trend_id: str
    regenerate_sections: Optional[List[str]] = Field(
        default=None,
        description="Sections to regenerate: title, description, body, image, code"
    )


@router.get("/", response_model=ContentListResponse)
async def list_content(
    trend_id: Optional[str] = Query(None, description="Filter by trend ID"),
    pagination: PaginationParams = Depends(get_pagination_params),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    content_repo: ContentRepository = Depends(get_content_repository)
):
    """List content with filtering and pagination."""
    logger.info(
        "List content request",
        user_id=current_user["id"],
        trend_id=trend_id,
        page=pagination.page,
        page_size=pagination.page_size
    )
    
    try:
        filters = {}
        if trend_id:
            filters["trend_id"] = trend_id
        
        result = await content_repo.list_with_pagination(
            page=pagination.page,
            page_size=pagination.page_size,
            filters=filters,
            order_by="created_at DESC"
        )
        
        return ContentListResponse(
            data=[ContentResponse(**content) for content in result["data"]],
            pagination=result["pagination"]
        )
    
    except Exception as e:
        logger.error("Failed to list content", error=str(e), user_id=current_user["id"])
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve content"
        )


@router.get("/{content_id}", response_model=ContentResponse)
async def get_content(
    content_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    content_repo: ContentRepository = Depends(get_content_repository)
):
    """Get specific content by ID."""
    logger.info("Get content", content_id=content_id, user_id=current_user["id"])
    
    try:
        content = await content_repo.get_by_id(content_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )
        
        return ContentResponse(**content)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get content", error=str(e), content_id=content_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve content"
        )


@router.get("/trend/{trend_id}", response_model=Optional[ContentResponse])
async def get_content_by_trend(
    trend_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    content_repo: ContentRepository = Depends(get_content_repository)
):
    """Get content for a specific trend."""
    logger.info("Get content by trend", trend_id=trend_id, user_id=current_user["id"])
    
    try:
        content = await content_repo.get_by_trend_id(trend_id)
        if not content:
            return None
        
        return ContentResponse(**content)
    
    except Exception as e:
        logger.error("Failed to get content by trend", error=str(e), trend_id=trend_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve content"
        )


@router.post("/generate", response_model=Dict[str, Any])
async def generate_content(
    request: ContentGenerationRequest,
    current_user: Dict[str, Any] = Depends(require_editor()),
    content_repo: ContentRepository = Depends(get_content_repository),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """Trigger content generation for a trend."""
    logger.info(
        "Generate content request",
        trend_id=request.trend_id,
        user_id=current_user["id"],
        regenerate_sections=request.regenerate_sections
    )
    
    try:
        # Verify trend exists and is approved
        trend = await trend_repo.get_by_id(request.trend_id)
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        if trend["status"] != "approved":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Trend must be approved before content generation"
            )
        
        # Check if content already exists
        existing_content = await content_repo.get_by_trend_id(request.trend_id)
        if existing_content and not request.regenerate_sections:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Content already exists for this trend. Use regenerate_sections to update specific parts."
            )
        
        # TODO: Trigger actual content generation task
        # For now, return a placeholder response
        task_id = f"content_gen_{request.trend_id}_{datetime.utcnow().timestamp()}"
        
        logger.info(
            "Content generation task created",
            task_id=task_id,
            trend_id=request.trend_id,
            user_id=current_user["id"]
        )
        
        return {
            "task_id": task_id,
            "status": "started",
            "trend_id": request.trend_id,
            "message": "Content generation task has been started"
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to generate content",
            error=str(e),
            trend_id=request.trend_id,
            user_id=current_user["id"]
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start content generation"
        )


@router.put("/{content_id}", response_model=ContentResponse)
async def update_content(
    content_id: str,
    updates: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(require_editor()),
    content_repo: ContentRepository = Depends(get_content_repository)
):
    """Update existing content."""
    logger.info(
        "Update content",
        content_id=content_id,
        user_id=current_user["id"],
        updates=list(updates.keys())
    )
    
    try:
        # Verify content exists
        existing_content = await content_repo.get_by_id(content_id)
        if not existing_content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )
        
        # Filter allowed updates
        allowed_fields = [
            "title", "description", "body", "meta_tags",
            "hero_image_url", "code_snippet", "code_language"
        ]
        filtered_updates = {k: v for k, v in updates.items() if k in allowed_fields}
        
        if not filtered_updates:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No valid fields to update"
            )
        
        # Update word count if body is updated
        if "body" in filtered_updates:
            filtered_updates["word_count"] = len(filtered_updates["body"].split())
        
        success = await content_repo.update(content_id, filtered_updates)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update content"
            )
        
        # Return updated content
        updated_content = await content_repo.get_by_id(content_id)
        logger.info("Content updated successfully", content_id=content_id)
        
        return ContentResponse(**updated_content)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to update content", error=str(e), content_id=content_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update content"
        )


@router.delete("/{content_id}")
async def delete_content(
    content_id: str,
    current_user: Dict[str, Any] = Depends(require_editor()),
    content_repo: ContentRepository = Depends(get_content_repository)
):
    """Delete content."""
    logger.info("Delete content", content_id=content_id, user_id=current_user["id"])
    
    try:
        # Verify content exists
        existing_content = await content_repo.get_by_id(content_id)
        if not existing_content:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )
        
        success = await content_repo.delete(content_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete content"
            )
        
        logger.info("Content deleted successfully", content_id=content_id)
        return {"message": "Content deleted successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to delete content", error=str(e), content_id=content_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete content"
        )


@router.get("/stats/overview", response_model=Dict[str, Any])
async def get_content_stats(
    current_user: Dict[str, Any] = Depends(require_editor()),
    content_repo: ContentRepository = Depends(get_content_repository)
):
    """Get content statistics overview."""
    logger.info("Get content stats", user_id=current_user["id"])
    
    try:
        # TODO: Implement actual statistics calculation
        # For now, return placeholder data
        stats = {
            "total_content": 0,
            "avg_word_count": 0,
            "avg_readability_score": 0,
            "content_by_category": {},
            "recent_content_count": 0
        }
        
        return stats
    
    except Exception as e:
        logger.error("Failed to get content stats", error=str(e), user_id=current_user["id"])
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve content statistics"
        )
