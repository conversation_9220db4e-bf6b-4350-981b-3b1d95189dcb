# Trend Platform Production Environment Configuration

# Application Settings
ENVIRONMENT=production
DEBUG=false
CORS_ORIGINS=https://yourdomain.com,https://api.yourdomain.com

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/trend_platform
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=30

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Authentication & Security
JWT_SECRET_KEY=your-super-secret-jwt-key-change-this-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
PASSWORD_HASH_ALGORITHM=bcrypt

# OpenAI Configuration
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=2000

# Coolify Configuration
COOLIFY_API_URL=https://coolify.yourdomain.com
COOLIFY_API_TOKEN=your-coolify-api-token

# Cloudflare Configuration
CLOUDFLARE_API_TOKEN=your-cloudflare-api-token
CLOUDFLARE_ZONE_ID=your-cloudflare-zone-id
BASE_DOMAIN=trends.yourdomain.com

# Git Configuration
GIT_USER=Trend Platform Deploy
GIT_EMAIL=<EMAIL>

# Domain & SSL Configuration
DOMAIN=yourdomain.com
SSL_EMAIL=<EMAIL>

# Frontend Configuration
VITE_API_URL=https://api.yourdomain.com
VITE_APP_TITLE=Trend Platform

# Monitoring Configuration
FLOWER_BASIC_AUTH=admin:secure-password
GRAFANA_ADMIN_PASSWORD=secure-grafana-password

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# File Upload Configuration
MAX_UPLOAD_SIZE=10485760  # 10MB
UPLOAD_PATH=/app/uploads

# Email Configuration (Optional)
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_TLS=true

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Performance Configuration
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Scraper Configuration
SCRAPER_USER_AGENTS=Mozilla/5.0 (compatible; TrendBot/1.0)
SCRAPER_DELAY_MIN=1
SCRAPER_DELAY_MAX=3
SCRAPER_TIMEOUT=30
SCRAPER_RETRIES=3

# Content Generation Configuration
CONTENT_MAX_LENGTH=2000
CONTENT_TEMPERATURE=0.7
IMAGE_GENERATION_ENABLED=true
CODE_GENERATION_ENABLED=true

# Deployment Configuration
DEPLOYMENT_MAX_CONCURRENT=3
DEPLOYMENT_TIMEOUT=1800
DEPLOYMENT_RETRY_COUNT=3

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3

# Security Configuration
ALLOWED_HOSTS=yourdomain.com,api.yourdomain.com
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000
SECURE_CONTENT_TYPE_NOSNIFF=true
SECURE_BROWSER_XSS_FILTER=true

# API Configuration
API_VERSION=v1
API_TITLE=Trend Platform API
API_DESCRIPTION=API for trend scraping and content generation platform
API_DOCS_URL=/docs
API_REDOC_URL=/redoc
