"""
Celery tasks for deployment operations.

Provides asynchronous task execution for deployment automation,
monitoring, and management operations.
"""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime
from celery import Celery
import structlog

from .deployment_orchestrator import DeploymentOrchestrator
from .deployment_tracker import DeploymentTracker
from .domain_manager import DomainManager
from database.connection import get_database_pool
from database.models.deployment_model import DeploymentRepository
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository
from app.config import settings

logger = structlog.get_logger()

# Use the same Celery app from other modules
from scraper.tasks import celery_app


async def get_deployment_repositories():
    """Get repository instances for deployment operations."""
    pool = await get_database_pool()
    deployment_repo = DeploymentRepository(pool)
    trend_repo = TrendRepository(pool)
    content_repo = ContentRepository(pool)
    return deployment_repo, trend_repo, content_repo


@celery_app.task(bind=True, name='deployment.tasks.deploy_trend_site_task')
def deploy_trend_site_task(
    self,
    trend_id: str,
    force_redeploy: bool = False,
    custom_domain: str = None
):
    """
    Celery task for deploying a trend site.
    
    Args:
        trend_id: ID of the trend to deploy
        force_redeploy: Whether to force redeployment
        custom_domain: Optional custom domain
    """
    task_id = self.request.id
    logger.info("Starting trend site deployment task", task_id=task_id, trend_id=trend_id)
    
    try:
        result = asyncio.run(_deploy_trend_site(trend_id, force_redeploy, custom_domain))
        
        logger.info(
            "Trend site deployment task completed",
            task_id=task_id,
            trend_id=trend_id,
            success=result["success"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Trend site deployment task failed", task_id=task_id, trend_id=trend_id, error=str(e))
        raise


async def _deploy_trend_site(
    trend_id: str,
    force_redeploy: bool = False,
    custom_domain: str = None
) -> Dict[str, Any]:
    """Deploy trend site asynchronously."""
    deployment_repo, trend_repo, content_repo = await get_deployment_repositories()
    
    # Get deployment configuration
    config = {
        "coolify": {
            "api_url": settings.coolify_api_url,
            "api_token": settings.coolify_api_token,
            "build_pack": "static"
        },
        "git": {
            "git_user": settings.git_user,
            "git_email": settings.git_email,
            "default_branch": "main"
        },
        "dns": {
            "cloudflare_api_token": settings.cloudflare_api_token,
            "zone_id": settings.cloudflare_zone_id,
            "base_domain": settings.base_domain
        }
    }
    
    orchestrator = DeploymentOrchestrator(
        deployment_repository=deployment_repo,
        trend_repository=trend_repo,
        content_repository=content_repo,
        config=config
    )
    
    try:
        result = await orchestrator.deploy_trend_site(
            trend_id=trend_id,
            force_redeploy=force_redeploy,
            custom_domain=custom_domain
        )
        return result
    finally:
        await orchestrator.close()


@celery_app.task(bind=True, name='deployment.tasks.batch_deploy_trends_task')
def batch_deploy_trends_task(
    self,
    trend_ids: List[str],
    max_concurrent: int = 3
):
    """
    Celery task for batch deployment of multiple trends.
    
    Args:
        trend_ids: List of trend IDs to deploy
        max_concurrent: Maximum concurrent deployments
    """
    task_id = self.request.id
    logger.info("Starting batch deployment task", task_id=task_id, trend_count=len(trend_ids))
    
    try:
        result = asyncio.run(_batch_deploy_trends(trend_ids, max_concurrent))
        
        logger.info(
            "Batch deployment task completed",
            task_id=task_id,
            total=result["total_trends"],
            successful=result["successful"],
            failed=result["failed"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Batch deployment task failed", task_id=task_id, error=str(e))
        raise


async def _batch_deploy_trends(
    trend_ids: List[str],
    max_concurrent: int = 3
) -> Dict[str, Any]:
    """Batch deploy trends asynchronously."""
    deployment_repo, trend_repo, content_repo = await get_deployment_repositories()
    
    config = {
        "coolify": {
            "api_url": settings.coolify_api_url,
            "api_token": settings.coolify_api_token
        },
        "dns": {
            "cloudflare_api_token": settings.cloudflare_api_token,
            "zone_id": settings.cloudflare_zone_id,
            "base_domain": settings.base_domain
        }
    }
    
    orchestrator = DeploymentOrchestrator(
        deployment_repository=deployment_repo,
        trend_repository=trend_repo,
        content_repository=content_repo,
        config=config
    )
    
    try:
        result = await orchestrator.batch_deploy_trends(
            trend_ids=trend_ids,
            max_concurrent=max_concurrent
        )
        return result
    finally:
        await orchestrator.close()


@celery_app.task(bind=True, name='deployment.tasks.deploy_approved_trends_task')
def deploy_approved_trends_task(self, limit: int = 10):
    """
    Deploy approved trends that have content but no deployment.
    
    Args:
        limit: Maximum number of trends to deploy
    """
    task_id = self.request.id
    logger.info("Starting approved trends deployment", task_id=task_id, limit=limit)
    
    try:
        result = asyncio.run(_deploy_approved_trends(limit))
        
        logger.info(
            "Approved trends deployment completed",
            task_id=task_id,
            processed=result["processed_count"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Approved trends deployment failed", task_id=task_id, error=str(e))
        raise


async def _deploy_approved_trends(limit: int) -> Dict[str, Any]:
    """Deploy approved trends that need deployment."""
    deployment_repo, trend_repo, content_repo = await get_deployment_repositories()
    
    try:
        # Get approved trends with content but no successful deployment
        approved_trends = await trend_repo.list_with_pagination(
            filters={"status": "approved"},
            page_size=limit * 2,  # Get more to filter
            order_by="created_at DESC"
        )
        
        # Filter trends that need deployment
        trends_to_deploy = []
        for trend in approved_trends["data"]:
            # Check if trend has content
            content = await content_repo.get_by_trend_id(trend["id"])
            if not content:
                continue
            
            # Check if trend has successful deployment
            deployment = await deployment_repo.get_successful_deployment(trend["id"])
            if deployment:
                continue
            
            trends_to_deploy.append(trend["id"])
            
            if len(trends_to_deploy) >= limit:
                break
        
        if not trends_to_deploy:
            return {
                "processed_count": 0,
                "message": "No approved trends need deployment"
            }
        
        # Deploy trends
        config = {
            "coolify": {
                "api_url": settings.coolify_api_url,
                "api_token": settings.coolify_api_token
            },
            "dns": {
                "cloudflare_api_token": settings.cloudflare_api_token,
                "zone_id": settings.cloudflare_zone_id,
                "base_domain": settings.base_domain
            }
        }
        
        orchestrator = DeploymentOrchestrator(
            deployment_repository=deployment_repo,
            trend_repository=trend_repo,
            content_repository=content_repo,
            config=config
        )
        
        batch_result = await orchestrator.batch_deploy_trends(
            trend_ids=trends_to_deploy,
            max_concurrent=3
        )
        
        return {
            "processed_count": len(trends_to_deploy),
            "successful": batch_result["successful"],
            "failed": batch_result["failed"],
            "batch_result": batch_result
        }
    
    finally:
        if 'orchestrator' in locals():
            await orchestrator.close()


@celery_app.task(bind=True, name='deployment.tasks.monitor_deployments_task')
def monitor_deployments_task(self):
    """Monitor active deployments and update their status."""
    task_id = self.request.id
    logger.info("Starting deployment monitoring", task_id=task_id)
    
    try:
        result = asyncio.run(_monitor_deployments())
        
        logger.info(
            "Deployment monitoring completed",
            task_id=task_id,
            monitored_count=result["monitored_count"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Deployment monitoring failed", task_id=task_id, error=str(e))
        raise


async def _monitor_deployments() -> Dict[str, Any]:
    """Monitor active deployments."""
    deployment_repo, _, _ = await get_deployment_repositories()
    
    # Get building deployments
    building_deployments = await deployment_repo.get_deployments_by_status("building")
    
    if not building_deployments:
        return {
            "monitored_count": 0,
            "message": "No active deployments to monitor"
        }
    
    # Initialize deployment tracker
    from .coolify_client import CoolifyClient
    
    coolify_client = CoolifyClient(
        api_url=settings.coolify_api_url,
        api_token=settings.coolify_api_token
    )
    
    tracker = DeploymentTracker(
        deployment_repository=deployment_repo,
        coolify_client=coolify_client
    )
    
    try:
        monitored_count = 0
        
        for deployment in building_deployments:
            if deployment.get("coolify_deployment_uuid"):
                # Start monitoring if not already monitored
                if deployment["id"] not in tracker.monitoring_tasks:
                    await tracker.start_deployment_tracking(
                        deployment_id=deployment["id"],
                        coolify_deployment_id=deployment["coolify_deployment_uuid"],
                        trend_id=deployment["trend_id"]
                    )
                    monitored_count += 1
        
        return {
            "monitored_count": monitored_count,
            "total_building": len(building_deployments)
        }
    
    finally:
        await coolify_client.close()


@celery_app.task(bind=True, name='deployment.tasks.setup_custom_domain_task')
def setup_custom_domain_task(
    self,
    deployment_id: str,
    custom_domain: str
):
    """
    Setup custom domain for a deployment.
    
    Args:
        deployment_id: Deployment ID
        custom_domain: Custom domain to setup
    """
    task_id = self.request.id
    logger.info("Starting custom domain setup", task_id=task_id, deployment_id=deployment_id)
    
    try:
        result = asyncio.run(_setup_custom_domain(deployment_id, custom_domain))
        
        logger.info(
            "Custom domain setup completed",
            task_id=task_id,
            deployment_id=deployment_id,
            success=result["success"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Custom domain setup failed", task_id=task_id, error=str(e))
        raise


async def _setup_custom_domain(deployment_id: str, custom_domain: str) -> Dict[str, Any]:
    """Setup custom domain for deployment."""
    deployment_repo, _, _ = await get_deployment_repositories()
    
    # Get deployment
    deployment = await deployment_repo.get_by_id(deployment_id)
    if not deployment:
        return {
            "success": False,
            "error": "Deployment not found"
        }
    
    # Initialize domain manager
    domain_manager = DomainManager({
        "cloudflare_api_token": settings.cloudflare_api_token,
        "zone_id": settings.cloudflare_zone_id,
        "base_domain": settings.base_domain
    })
    
    try:
        # Setup DNS
        dns_result = await domain_manager.create_cname_record(
            domain=custom_domain,
            target=deployment["deploy_url"]
        )
        
        if dns_result["success"]:
            # Update deployment record
            await deployment_repo.update(deployment_id, {
                "custom_domain": custom_domain,
                "dns_configured": True
            })
        
        return dns_result
    
    finally:
        await domain_manager.close()


@celery_app.task(bind=True, name='deployment.tasks.cleanup_failed_deployments_task')
def cleanup_failed_deployments_task(self, max_age_hours: int = 24):
    """
    Clean up old failed deployments.
    
    Args:
        max_age_hours: Maximum age of failed deployments to keep
    """
    task_id = self.request.id
    logger.info("Starting failed deployments cleanup", task_id=task_id)
    
    try:
        result = asyncio.run(_cleanup_failed_deployments(max_age_hours))
        
        logger.info(
            "Failed deployments cleanup completed",
            task_id=task_id,
            cleaned_count=result["cleaned_count"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Failed deployments cleanup failed", task_id=task_id, error=str(e))
        raise


async def _cleanup_failed_deployments(max_age_hours: int) -> Dict[str, Any]:
    """Clean up old failed deployments."""
    deployment_repo, _, _ = await get_deployment_repositories()
    
    # Calculate days from hours
    days = max_age_hours / 24
    
    cleaned_count = await deployment_repo.cleanup_old_deployments(days)
    
    return {
        "cleaned_count": cleaned_count,
        "max_age_hours": max_age_hours
    }


@celery_app.task(bind=True, name='deployment.tasks.get_deployment_metrics_task')
def get_deployment_metrics_task(self, days: int = 7):
    """Get deployment metrics and statistics."""
    task_id = self.request.id
    logger.info("Getting deployment metrics", task_id=task_id, days=days)
    
    try:
        result = asyncio.run(_get_deployment_metrics(days))
        return result
    
    except Exception as e:
        logger.error("Failed to get deployment metrics", task_id=task_id, error=str(e))
        raise


async def _get_deployment_metrics(days: int) -> Dict[str, Any]:
    """Get deployment metrics."""
    deployment_repo, _, _ = await get_deployment_repositories()
    
    # Get deployment stats
    stats = await deployment_repo.get_deployment_stats()
    
    # Get recent deployments
    recent_deployments = await deployment_repo.get_recent_deployments(days)
    
    return {
        "stats": stats,
        "recent_deployments": len(recent_deployments),
        "period_days": days,
        "timestamp": datetime.utcnow().isoformat()
    }


# Update Celery beat schedule to include deployment tasks
celery_app.conf.beat_schedule.update({
    'deploy-approved-trends': {
        'task': 'deployment.tasks.deploy_approved_trends_task',
        'schedule': 600.0,  # Every 10 minutes
        'args': (5,)  # Deploy up to 5 trends at a time
    },
    'monitor-deployments': {
        'task': 'deployment.tasks.monitor_deployments_task',
        'schedule': 60.0,  # Every minute
        'args': ()
    },
    'cleanup-failed-deployments': {
        'task': 'deployment.tasks.cleanup_failed_deployments_task',
        'schedule': 3600.0,  # Every hour
        'args': (24,)  # Clean up deployments older than 24 hours
    }
})

# Task routing for deployment operations
celery_app.conf.task_routes.update({
    'deployment.tasks.*': {'queue': 'deployment'},
})
