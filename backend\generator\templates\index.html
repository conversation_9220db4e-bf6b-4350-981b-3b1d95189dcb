{% extends "base.html" %}

{% block title %}{{ page_title | default('Latest Trending Topics') }} - {{ site_name }}{% endblock %}

{% block structured_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "WebSite",
  "name": "{{ site_name }}",
  "url": "{{ canonical_url | default('') }}",
  "description": "{{ meta_description }}",
  "publisher": {
    "@type": "Organization",
    "name": "{{ site_name }}"
  },
  "potentialAction": {
    "@type": "SearchAction",
    "target": "{{ canonical_url | default('') }}/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
}
</script>
{% endblock %}

{% block body_class %}homepage{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1 class="hero-title">Discover What's Trending Now</h1>
            <p class="hero-subtitle">
                Stay ahead of the curve with real-time insights into the latest trending topics 
                across technology, health, business, and more.
            </p>
            
            {% if featured_article %}
            <div class="featured-article">
                <h2>Featured Trending Topic</h2>
                <article class="featured-card">
                    <a href="{{ featured_article.url }}" class="featured-link">
                        {% if featured_article.image %}
                        <div class="featured-image">
                            <img src="{{ featured_article.image }}" alt="{{ featured_article.title }}" loading="lazy">
                        </div>
                        {% endif %}
                        <div class="featured-content">
                            <span class="category-badge category-{{ featured_article.category | lower | slug }}">
                                {{ featured_article.category }}
                            </span>
                            <h3 class="featured-title">{{ featured_article.title }}</h3>
                            <p class="featured-excerpt">{{ featured_article.excerpt | truncate_words(30) }}</p>
                            <div class="featured-meta">
                                <time class="featured-date">{{ featured_article.date.strftime('%B %d, %Y') }}</time>
                                <span class="reading-time">{{ featured_article.reading_time }} min read</span>
                            </div>
                        </div>
                    </a>
                </article>
            </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Trending Topics Section -->
<section class="trending-topics">
    <div class="container">
        <header class="section-header">
            <h2>Latest Trending Topics</h2>
            <p>Explore the most popular and emerging trends across different categories</p>
        </header>

        {% if trending_topics %}
        <!-- Category Filter -->
        <div class="category-filter">
            <button class="filter-btn active" data-category="all">All Categories</button>
            {% for category in trending_topics | map(attribute='category') | unique %}
            <button class="filter-btn" data-category="{{ category | lower | slug }}">{{ category }}</button>
            {% endfor %}
        </div>

        <!-- Topics Grid -->
        <div class="topics-grid">
            {% for topic in trending_topics %}
            <article class="topic-card" data-category="{{ topic.category | lower | slug }}">
                <a href="{{ topic.url }}" class="topic-link">
                    {% if topic.image %}
                    <div class="topic-image">
                        <img src="{{ topic.image }}" alt="{{ topic.title }}" loading="lazy">
                    </div>
                    {% endif %}
                    
                    <div class="topic-content">
                        <div class="topic-header">
                            <span class="category-badge category-{{ topic.category | lower | slug }}">
                                {{ topic.category }}
                            </span>
                            {% if topic.score %}
                            <span class="trend-score" title="Trend Score">{{ topic.score | round(1) }}</span>
                            {% endif %}
                        </div>
                        
                        <h3 class="topic-title">{{ topic.title }}</h3>
                        <p class="topic-excerpt">{{ topic.excerpt | truncate_words(25) }}</p>
                        
                        <div class="topic-meta">
                            <time class="topic-date">{{ topic.date.strftime('%B %d, %Y') }}</time>
                            <span class="reading-time">{{ topic.reading_time }} min read</span>
                            {% if topic.search_volume %}
                            <span class="search-volume" title="Search Volume">
                                {{ topic.search_volume | format_number }} searches
                            </span>
                            {% endif %}
                        </div>
                        
                        {% if topic.tags %}
                        <div class="topic-tags">
                            {% for tag in topic.tags[:3] %}
                            <span class="tag">{{ tag }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </a>
            </article>
            {% endfor %}
        </div>

        <!-- Load More Button -->
        <div class="load-more-section">
            <button class="load-more-btn" onclick="loadMoreTopics()">
                Load More Trending Topics
            </button>
        </div>
        {% else %}
        <div class="no-topics">
            <h3>No trending topics available</h3>
            <p>Check back soon for the latest trends!</p>
        </div>
        {% endif %}
    </div>
</section>

<!-- Categories Overview -->
<section class="categories-overview">
    <div class="container">
        <header class="section-header">
            <h2>Explore by Category</h2>
            <p>Dive deeper into specific areas of interest</p>
        </header>
        
        <div class="categories-grid">
            <div class="category-item">
                <a href="/category/technology" class="category-link">
                    <div class="category-icon">🚀</div>
                    <h3>Technology</h3>
                    <p>Latest tech trends, innovations, and breakthroughs</p>
                </a>
            </div>
            
            <div class="category-item">
                <a href="/category/health" class="category-link">
                    <div class="category-icon">🏥</div>
                    <h3>Health</h3>
                    <p>Health trends, medical advances, and wellness topics</p>
                </a>
            </div>
            
            <div class="category-item">
                <a href="/category/business" class="category-link">
                    <div class="category-icon">💼</div>
                    <h3>Business</h3>
                    <p>Market trends, startup news, and business insights</p>
                </a>
            </div>
            
            <div class="category-item">
                <a href="/category/entertainment" class="category-link">
                    <div class="category-icon">🎬</div>
                    <h3>Entertainment</h3>
                    <p>Pop culture, movies, music, and entertainment news</p>
                </a>
            </div>
            
            <div class="category-item">
                <a href="/category/science" class="category-link">
                    <div class="category-icon">🔬</div>
                    <h3>Science</h3>
                    <p>Scientific discoveries, research, and innovations</p>
                </a>
            </div>
            
            <div class="category-item">
                <a href="/category/sports" class="category-link">
                    <div class="category-icon">⚽</div>
                    <h3>Sports</h3>
                    <p>Sports news, events, and trending athletes</p>
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Signup -->
<section class="newsletter-signup">
    <div class="container">
        <div class="newsletter-content">
            <h2>Stay Updated</h2>
            <p>Get the latest trending topics delivered to your inbox</p>
            
            <form class="newsletter-form" onsubmit="subscribeNewsletter(event)">
                <div class="form-group">
                    <input type="email" placeholder="Enter your email address" required>
                    <button type="submit">Subscribe</button>
                </div>
                <p class="form-note">We respect your privacy. Unsubscribe at any time.</p>
            </form>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/assets/css/homepage.css">
{% endblock %}

{% block extra_js %}
<script>
// Category filtering
document.addEventListener('DOMContentLoaded', function() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    const topicCards = document.querySelectorAll('.topic-card');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.dataset.category;
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter cards
            topicCards.forEach(card => {
                if (category === 'all' || card.dataset.category === category) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    });
});

// Load more topics
function loadMoreTopics() {
    // This would typically make an AJAX request to load more content
    console.log('Loading more topics...');
    
    // For now, just show a message
    const button = document.querySelector('.load-more-btn');
    button.textContent = 'Loading...';
    button.disabled = true;
    
    setTimeout(() => {
        button.textContent = 'Load More Trending Topics';
        button.disabled = false;
    }, 2000);
}

// Newsletter subscription
function subscribeNewsletter(event) {
    event.preventDefault();
    
    const form = event.target;
    const email = form.querySelector('input[type="email"]').value;
    const button = form.querySelector('button');
    
    button.textContent = 'Subscribing...';
    button.disabled = true;
    
    // Simulate subscription
    setTimeout(() => {
        alert('Thank you for subscribing!');
        form.reset();
        button.textContent = 'Subscribe';
        button.disabled = false;
    }, 1500);
}

// Track homepage interactions
if (typeof gtag !== 'undefined') {
    // Track category clicks
    document.querySelectorAll('.category-link').forEach(link => {
        link.addEventListener('click', function() {
            gtag('event', 'category_click', {
                'category_name': this.querySelector('h3').textContent
            });
        });
    });
    
    // Track topic clicks
    document.querySelectorAll('.topic-link').forEach(link => {
        link.addEventListener('click', function() {
            const card = this.closest('.topic-card');
            gtag('event', 'topic_click', {
                'topic_category': card.dataset.category,
                'topic_title': this.querySelector('.topic-title').textContent
            });
        });
    });
}
</script>
{% endblock %}
