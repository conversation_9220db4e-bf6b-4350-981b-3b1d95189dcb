#!/bin/bash

# Trend Platform Production Deployment Script
# For OCI Ampere A1 Infrastructure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="trend-platform"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env.prod"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root for security reasons"
    fi
}

# Check system requirements
check_requirements() {
    log "Checking system requirements..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check available disk space (minimum 10GB)
    available_space=$(df / | awk 'NR==2 {print $4}')
    if [[ $available_space -lt 10485760 ]]; then
        warning "Less than 10GB disk space available. Consider freeing up space."
    fi
    
    # Check available memory (minimum 2GB)
    available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [[ $available_memory -lt 2048 ]]; then
        warning "Less than 2GB memory available. Performance may be affected."
    fi
    
    success "System requirements check completed"
}

# Load environment variables
load_environment() {
    log "Loading environment configuration..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        error "Environment file $ENV_FILE not found. Please create it from .env.example"
    fi
    
    # Source environment file
    set -a
    source "$ENV_FILE"
    set +a
    
    # Validate required environment variables
    required_vars=(
        "DATABASE_URL"
        "REDIS_URL"
        "OPENAI_API_KEY"
        "COOLIFY_API_URL"
        "COOLIFY_API_TOKEN"
        "JWT_SECRET_KEY"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            error "Required environment variable $var is not set"
        fi
    done
    
    success "Environment configuration loaded"
}

# Create necessary directories
setup_directories() {
    log "Setting up directories..."
    
    directories=(
        "./logs"
        "./logs/nginx"
        "./generated_sites"
        "./ssl"
        "$BACKUP_DIR"
        "./monitoring"
        "./monitoring/grafana/dashboards"
        "./monitoring/grafana/datasources"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log "Created directory: $dir"
    done
    
    success "Directories setup completed"
}

# Setup SSL certificates (Let's Encrypt)
setup_ssl() {
    log "Setting up SSL certificates..."
    
    if [[ -n "$DOMAIN" ]]; then
        if command -v certbot &> /dev/null; then
            log "Generating SSL certificate for $DOMAIN"
            
            # Stop nginx if running
            docker-compose -f "$DOCKER_COMPOSE_FILE" stop nginx 2>/dev/null || true
            
            # Generate certificate
            certbot certonly --standalone \
                --email "$SSL_EMAIL" \
                --agree-tos \
                --no-eff-email \
                -d "$DOMAIN" \
                --non-interactive
            
            # Copy certificates to ssl directory
            cp "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" "./ssl/cert.pem"
            cp "/etc/letsencrypt/live/$DOMAIN/privkey.pem" "./ssl/key.pem"
            
            success "SSL certificate generated for $DOMAIN"
        else
            warning "Certbot not installed. SSL setup skipped."
            warning "You can install certbot and run this script again for SSL support."
        fi
    else
        warning "DOMAIN not set in environment. SSL setup skipped."
    fi
}

# Setup monitoring configuration
setup_monitoring() {
    log "Setting up monitoring configuration..."
    
    # Create Prometheus configuration
    cat > "./monitoring/prometheus.yml" << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'trend-platform-api'
    static_configs:
      - targets: ['api:8000']
    metrics_path: '/metrics'

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
EOF

    # Create Grafana datasource configuration
    cat > "./monitoring/grafana/datasources/prometheus.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    success "Monitoring configuration setup completed"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."
    
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="$BACKUP_DIR/backup_$timestamp.tar.gz"
    
    # Create backup of important directories
    tar -czf "$backup_file" \
        --exclude='./logs/*' \
        --exclude='./backups/*' \
        --exclude='./.git/*' \
        --exclude='./node_modules/*' \
        --exclude='./venv/*' \
        . 2>/dev/null || true
    
    if [[ -f "$backup_file" ]]; then
        success "Backup created: $backup_file"
    else
        warning "Backup creation failed or no data to backup"
    fi
}

# Pull latest images
pull_images() {
    log "Pulling latest Docker images..."
    
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    success "Docker images pulled successfully"
}

# Build custom images
build_images() {
    log "Building custom Docker images..."
    
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    success "Docker images built successfully"
}

# Deploy services
deploy_services() {
    log "Deploying services..."
    
    # Stop existing services
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Start services
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    success "Services deployed successfully"
}

# Health check
health_check() {
    log "Performing health checks..."
    
    # Wait for services to start
    sleep 30
    
    # Check API health
    if curl -f http://localhost:8000/health &>/dev/null; then
        success "API health check passed"
    else
        error "API health check failed"
    fi
    
    # Check dashboard
    if curl -f http://localhost:3000/health &>/dev/null; then
        success "Dashboard health check passed"
    else
        warning "Dashboard health check failed"
    fi
    
    # Check Redis
    if docker-compose -f "$DOCKER_COMPOSE_FILE" exec -T redis redis-cli ping | grep -q PONG; then
        success "Redis health check passed"
    else
        error "Redis health check failed"
    fi
    
    success "Health checks completed"
}

# Setup log rotation
setup_log_rotation() {
    log "Setting up log rotation..."
    
    cat > "/tmp/trend-platform-logrotate" << EOF
./logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $(whoami) $(whoami)
    postrotate
        docker-compose -f $PWD/$DOCKER_COMPOSE_FILE restart nginx
    endscript
}
EOF

    # Install logrotate configuration
    sudo mv "/tmp/trend-platform-logrotate" "/etc/logrotate.d/trend-platform"
    
    success "Log rotation setup completed"
}

# Display deployment information
show_deployment_info() {
    log "Deployment completed successfully!"
    echo
    echo "=== Trend Platform Deployment Information ==="
    echo "Dashboard URL: http://localhost:3000"
    echo "API URL: http://localhost:8000"
    echo "API Documentation: http://localhost:8000/docs"
    echo "Flower Monitoring: http://localhost:5555"
    echo "Prometheus: http://localhost:9090"
    echo "Grafana: http://localhost:3001"
    echo
    echo "=== Service Status ==="
    docker-compose -f "$DOCKER_COMPOSE_FILE" ps
    echo
    echo "=== Useful Commands ==="
    echo "View logs: docker-compose -f $DOCKER_COMPOSE_FILE logs -f [service]"
    echo "Restart service: docker-compose -f $DOCKER_COMPOSE_FILE restart [service]"
    echo "Stop all: docker-compose -f $DOCKER_COMPOSE_FILE down"
    echo "Update: ./deploy.sh --update"
    echo
}

# Update deployment
update_deployment() {
    log "Updating deployment..."
    
    backup_data
    pull_images
    build_images
    deploy_services
    health_check
    
    success "Deployment updated successfully"
}

# Main deployment function
main() {
    log "Starting Trend Platform deployment..."
    
    check_root
    check_requirements
    load_environment
    setup_directories
    setup_monitoring
    
    if [[ "$1" == "--update" ]]; then
        update_deployment
    else
        setup_ssl
        backup_data
        pull_images
        build_images
        deploy_services
        setup_log_rotation
    fi
    
    health_check
    show_deployment_info
}

# Handle script arguments
case "$1" in
    --update)
        main --update
        ;;
    --backup)
        load_environment
        backup_data
        ;;
    --health)
        health_check
        ;;
    --logs)
        docker-compose -f "$DOCKER_COMPOSE_FILE" logs -f "${2:-}"
        ;;
    --stop)
        docker-compose -f "$DOCKER_COMPOSE_FILE" down
        ;;
    --restart)
        docker-compose -f "$DOCKER_COMPOSE_FILE" restart "${2:-}"
        ;;
    *)
        main
        ;;
esac
