"""
Database connection management for the Trend Platform.

Provides connection pooling, health checks, and connection lifecycle management
using asyncpg for PostgreSQL.
"""

import asyncio
import logging
from typing import Optional
import asyncpg
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections and connection pooling."""
    
    def __init__(self, database_url: str, min_connections: int = 5, max_connections: int = 20):
        self.database_url = database_url
        self.min_connections = min_connections
        self.max_connections = max_connections
        self._pool: Optional[asyncpg.Pool] = None
        self._lock = asyncio.Lock()
    
    async def create_pool(self) -> asyncpg.Pool:
        """Create and return a connection pool."""
        if self._pool is None:
            async with self._lock:
                if self._pool is None:
                    logger.info("Creating database connection pool")
                    self._pool = await asyncpg.create_pool(
                        self.database_url,
                        min_size=self.min_connections,
                        max_size=self.max_connections,
                        command_timeout=60,
                        server_settings={
                            'jit': 'off',  # Disable JIT for better performance on small queries
                            'application_name': 'trend_platform'
                        }
                    )
                    logger.info(
                        f"Database pool created with {self.min_connections}-{self.max_connections} connections"
                    )
        return self._pool
    
    async def close_pool(self):
        """Close the connection pool."""
        if self._pool:
            async with self._lock:
                if self._pool:
                    logger.info("Closing database connection pool")
                    await self._pool.close()
                    self._pool = None
    
    async def health_check(self) -> bool:
        """Check if database is healthy and accessible."""
        try:
            pool = await self.create_pool()
            async with pool.acquire() as conn:
                await conn.fetchval("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    @asynccontextmanager
    async def get_connection(self):
        """Get a database connection from the pool."""
        pool = await self.create_pool()
        async with pool.acquire() as conn:
            try:
                yield conn
            except Exception as e:
                logger.error(f"Database operation failed: {e}")
                raise
    
    @asynccontextmanager
    async def get_transaction(self):
        """Get a database connection with transaction context."""
        pool = await self.create_pool()
        async with pool.acquire() as conn:
            async with conn.transaction():
                try:
                    yield conn
                except Exception as e:
                    logger.error(f"Database transaction failed: {e}")
                    raise


# Global database manager instance
_db_manager: Optional[DatabaseManager] = None


def initialize_database(database_url: str, min_connections: int = 5, max_connections: int = 20):
    """Initialize the global database manager."""
    global _db_manager
    _db_manager = DatabaseManager(database_url, min_connections, max_connections)
    logger.info("Database manager initialized")


async def get_database_pool() -> asyncpg.Pool:
    """Get the database connection pool."""
    if _db_manager is None:
        raise RuntimeError("Database not initialized. Call initialize_database() first.")
    return await _db_manager.create_pool()


async def close_database():
    """Close the database connection pool."""
    if _db_manager:
        await _db_manager.close_pool()


async def database_health_check() -> bool:
    """Check database health."""
    if _db_manager is None:
        return False
    return await _db_manager.health_check()


@asynccontextmanager
async def get_db_connection():
    """Get a database connection context manager."""
    if _db_manager is None:
        raise RuntimeError("Database not initialized. Call initialize_database() first.")
    async with _db_manager.get_connection() as conn:
        yield conn


@asynccontextmanager
async def get_db_transaction():
    """Get a database transaction context manager."""
    if _db_manager is None:
        raise RuntimeError("Database not initialized. Call initialize_database() first.")
    async with _db_manager.get_transaction() as conn:
        yield conn
