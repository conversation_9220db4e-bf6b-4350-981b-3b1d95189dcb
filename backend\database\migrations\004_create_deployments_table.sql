-- Migration: Create deployments table
-- Description: Creates the deployments table for tracking deployment status and metadata

-- Create deployments table
CREATE TABLE IF NOT EXISTS deployments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trend_id UUID NOT NULL REFERENCES trends(id) ON DELETE CASCADE,
    content_id UUID REFERENCES content(id) ON DELETE SET NULL,
    
    -- Deployment status and progress
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    
    -- Domain configuration
    custom_domain VARCHAR(255),
    deploy_url VARCHAR(500),
    dns_configured BOOLEAN DEFAULT FALSE,
    
    -- External service IDs
    coolify_app_uuid VARCHAR(255),
    coolify_deployment_uuid VARCHAR(255),
    
    -- Git information
    git_commit_sha VARCHAR(40),
    git_repository_url VARCHAR(500),
    git_branch VARCHAR(100) DEFAULT 'main',
    
    -- Build information
    build_duration INTEGER, -- in seconds
    build_log TEXT,
    error_message TEXT,
    
    -- Retry tracking
    retry_count INTEGER DEFAULT 0,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deployed_at TIMESTAMP WITH TIME ZONE,
    
    -- Constraints
    CONSTRAINT valid_status CHECK (status IN ('pending', 'building', 'success', 'failed', 'cancelled')),
    CONSTRAINT valid_retry_count CHECK (retry_count >= 0)
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_deployments_trend_id ON deployments(trend_id);
CREATE INDEX IF NOT EXISTS idx_deployments_status ON deployments(status);
CREATE INDEX IF NOT EXISTS idx_deployments_created_at ON deployments(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_deployments_coolify_app ON deployments(coolify_app_uuid);
CREATE INDEX IF NOT EXISTS idx_deployments_custom_domain ON deployments(custom_domain);

-- Create partial indexes for active deployments
CREATE INDEX IF NOT EXISTS idx_deployments_active ON deployments(created_at DESC) 
WHERE status IN ('pending', 'building');

-- Create composite index for trend deployment history
CREATE INDEX IF NOT EXISTS idx_deployments_trend_history ON deployments(trend_id, created_at DESC);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_deployments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    
    -- Set deployed_at when status changes to success
    IF NEW.status = 'success' AND OLD.status != 'success' THEN
        NEW.deployed_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_deployments_updated_at
    BEFORE UPDATE ON deployments
    FOR EACH ROW
    EXECUTE FUNCTION update_deployments_updated_at();

-- Add comments for documentation
COMMENT ON TABLE deployments IS 'Tracks deployment status and metadata for trend sites';
COMMENT ON COLUMN deployments.id IS 'Unique deployment identifier';
COMMENT ON COLUMN deployments.trend_id IS 'Reference to the trend being deployed';
COMMENT ON COLUMN deployments.content_id IS 'Reference to the content being deployed';
COMMENT ON COLUMN deployments.status IS 'Current deployment status';
COMMENT ON COLUMN deployments.progress IS 'Deployment progress percentage (0-100)';
COMMENT ON COLUMN deployments.custom_domain IS 'Custom domain for the deployment';
COMMENT ON COLUMN deployments.deploy_url IS 'URL where the site is deployed';
COMMENT ON COLUMN deployments.dns_configured IS 'Whether DNS has been configured';
COMMENT ON COLUMN deployments.coolify_app_uuid IS 'Coolify application UUID';
COMMENT ON COLUMN deployments.coolify_deployment_uuid IS 'Coolify deployment UUID';
COMMENT ON COLUMN deployments.git_commit_sha IS 'Git commit SHA for the deployment';
COMMENT ON COLUMN deployments.build_duration IS 'Build duration in seconds';
COMMENT ON COLUMN deployments.build_log IS 'Build log output';
COMMENT ON COLUMN deployments.error_message IS 'Error message if deployment failed';
COMMENT ON COLUMN deployments.retry_count IS 'Number of retry attempts';

-- Create view for deployment statistics
CREATE OR REPLACE VIEW deployment_stats AS
SELECT 
    DATE(created_at) as date,
    status,
    COUNT(*) as count,
    AVG(build_duration) as avg_build_duration,
    MIN(build_duration) as min_build_duration,
    MAX(build_duration) as max_build_duration
FROM deployments 
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at), status
ORDER BY date DESC, status;

COMMENT ON VIEW deployment_stats IS 'Daily deployment statistics for the last 30 days';

-- Create view for active deployments
CREATE OR REPLACE VIEW active_deployments AS
SELECT 
    d.id,
    d.trend_id,
    t.keyword,
    t.slug,
    d.status,
    d.progress,
    d.deploy_url,
    d.custom_domain,
    d.created_at,
    d.updated_at,
    EXTRACT(EPOCH FROM (CURRENT_TIMESTAMP - d.created_at)) as duration_seconds
FROM deployments d
JOIN trends t ON d.trend_id = t.id
WHERE d.status IN ('pending', 'building')
ORDER BY d.created_at DESC;

COMMENT ON VIEW active_deployments IS 'Currently active deployments with trend information';

-- Create function to get deployment history for a trend
CREATE OR REPLACE FUNCTION get_trend_deployment_history(trend_uuid UUID)
RETURNS TABLE (
    deployment_id UUID,
    status VARCHAR(50),
    deploy_url VARCHAR(500),
    custom_domain VARCHAR(255),
    build_duration INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    deployed_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id,
        d.status,
        d.deploy_url,
        d.custom_domain,
        d.build_duration,
        d.created_at,
        d.deployed_at
    FROM deployments d
    WHERE d.trend_id = trend_uuid
    ORDER BY d.created_at DESC;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION get_trend_deployment_history IS 'Get deployment history for a specific trend';

-- Create function to cleanup old deployments
CREATE OR REPLACE FUNCTION cleanup_old_deployments(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete old failed and cancelled deployments
    DELETE FROM deployments 
    WHERE created_at < CURRENT_TIMESTAMP - (days_to_keep || ' days')::INTERVAL
    AND status IN ('failed', 'cancelled');
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Keep successful deployments longer (double the retention period)
    DELETE FROM deployments 
    WHERE created_at < CURRENT_TIMESTAMP - ((days_to_keep * 2) || ' days')::INTERVAL
    AND status = 'success';
    
    GET DIAGNOSTICS deleted_count = deleted_count + ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION cleanup_old_deployments IS 'Clean up old deployment records';

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON deployments TO trend_platform_user;
GRANT SELECT ON deployment_stats TO trend_platform_user;
GRANT SELECT ON active_deployments TO trend_platform_user;
GRANT EXECUTE ON FUNCTION get_trend_deployment_history TO trend_platform_user;
GRANT EXECUTE ON FUNCTION cleanup_old_deployments TO trend_platform_user;
