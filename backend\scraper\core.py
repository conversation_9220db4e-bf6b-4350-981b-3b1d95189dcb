"""
Core orchestration module for trend scraping.

Coordinates the entire scraping process including data collection,
processing, validation, deduplication, scoring, and storage.
"""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime
import structlog

from .sources.base_scraper import TrendData
from .sources.google_trends import GoogleTrendsScraper
from .sources.trends24_scraper import Trends24<PERSON>craper
from .filters import FilterPipeline, RegionFilter, CategoryFilter, KeywordFilter, QualityFilter, DuplicateFilter
from .validators import TrendValidator
from .deduplication import DeduplicationEng<PERSON>, FuzzyMatcher
from .scoring import TrendScorer, AdaptiveScorer
from database.models.trend_model import TrendRepository
from app.config import SCRAPER_CONFIG

logger = structlog.get_logger()


class TrendOrchestrator:
    """Main orchestrator for the trend scraping pipeline."""
    
    def __init__(
        self,
        trend_repository: TrendRepository,
        config: Dict[str, Any] = None
    ):
        """
        Initialize trend orchestrator.
        
        Args:
            trend_repository: Database repository for storing trends
            config: Configuration dictionary
        """
        self.trend_repository = trend_repository
        self.config = config or SCRAPER_CONFIG
        
        # Initialize components
        self.scrapers = self._initialize_scrapers()
        self.filter_pipeline = self._initialize_filters()
        self.validator = TrendValidator()
        self.deduplicator = DeduplicationEngine()
        self.scorer = AdaptiveScorer()
        
        # Statistics tracking
        self.stats = {
            "total_scraped": 0,
            "total_filtered": 0,
            "total_validated": 0,
            "total_deduplicated": 0,
            "total_stored": 0,
            "errors": []
        }
        
        self.logger = structlog.get_logger().bind(component="trend_orchestrator")
    
    def _initialize_scrapers(self) -> Dict[str, Any]:
        """Initialize scraper instances."""
        scrapers = {}
        
        # Google Trends scraper
        google_config = {
            "rate_limit_rpm": self.config.get("rate_limiting", {}).get("google_trends", {}).get("requests_per_minute", 10),
            "proxy_list": self.config.get("proxy_rotation", {}).get("proxy_list", []),
            "timeout": 30
        }
        scrapers["google_trends"] = GoogleTrendsScraper(google_config)
        
        # Trends24 scraper
        trends24_config = {
            "rate_limit_rpm": self.config.get("rate_limiting", {}).get("trends24", {}).get("requests_per_minute", 30),
            "timeout": 20
        }
        scrapers["trends24"] = Trends24Scraper(trends24_config)
        
        return scrapers
    
    def _initialize_filters(self) -> FilterPipeline:
        """Initialize filter pipeline."""
        pipeline = FilterPipeline()
        
        # Region filter
        allowed_regions = self.config.get("regions", ["US", "UK", "CA", "AU"])
        pipeline.add_filter(RegionFilter(allowed_regions=allowed_regions))
        
        # Category filter
        allowed_categories = self.config.get("categories", [
            "Technology", "Health", "Entertainment", "Business", "Science"
        ])
        pipeline.add_filter(CategoryFilter(allowed_categories=allowed_categories))
        
        # Quality filter
        min_score = self.config.get("scoring", {}).get("minimum_score", 50)
        pipeline.add_filter(QualityFilter(min_confidence=0.5))
        
        # Duplicate filter
        pipeline.add_filter(DuplicateFilter(similarity_threshold=0.8))
        
        return pipeline
    
    async def scrape_all_sources(
        self,
        regions: List[str] = None,
        categories: List[str] = None,
        max_trends_per_source: int = 50
    ) -> List[TrendData]:
        """
        Scrape trends from all configured sources.
        
        Args:
            regions: List of regions to scrape (defaults to config)
            categories: List of categories to scrape (defaults to config)
            max_trends_per_source: Maximum trends per source per region/category
            
        Returns:
            List of scraped trend data
        """
        regions = regions or self.config.get("regions", ["US"])
        categories = categories or self.config.get("categories", ["Technology"])
        
        self.logger.info(
            "Starting trend scraping",
            regions=regions,
            categories=categories,
            sources=list(self.scrapers.keys())
        )
        
        all_trends = []
        
        # Scrape from each source
        for source_name, scraper in self.scrapers.items():
            try:
                async with scraper:
                    source_trends = await self._scrape_source(
                        scraper, source_name, regions, categories, max_trends_per_source
                    )
                    all_trends.extend(source_trends)
                    
                    self.logger.info(
                        "Source scraping completed",
                        source=source_name,
                        trends_found=len(source_trends)
                    )
            
            except Exception as e:
                error_msg = f"Failed to scrape from {source_name}: {str(e)}"
                self.logger.error("Source scraping failed", source=source_name, error=str(e))
                self.stats["errors"].append(error_msg)
        
        self.stats["total_scraped"] = len(all_trends)
        
        self.logger.info(
            "All sources scraping completed",
            total_trends=len(all_trends),
            sources_scraped=len(self.scrapers)
        )
        
        return all_trends
    
    async def _scrape_source(
        self,
        scraper,
        source_name: str,
        regions: List[str],
        categories: List[str],
        max_trends: int
    ) -> List[TrendData]:
        """Scrape trends from a single source."""
        source_trends = []
        
        for region in regions:
            for category in categories:
                try:
                    trends = await scraper.scrape_trends(region, category)
                    
                    # Limit trends per region/category
                    if len(trends) > max_trends:
                        trends = trends[:max_trends]
                    
                    source_trends.extend(trends)
                    
                    self.logger.debug(
                        "Region/category scraped",
                        source=source_name,
                        region=region,
                        category=category,
                        trends_found=len(trends)
                    )
                    
                    # Small delay between requests
                    await asyncio.sleep(1)
                
                except Exception as e:
                    error_msg = f"Failed to scrape {source_name} for {region}/{category}: {str(e)}"
                    self.logger.warning(
                        "Region/category scraping failed",
                        source=source_name,
                        region=region,
                        category=category,
                        error=str(e)
                    )
                    self.stats["errors"].append(error_msg)
        
        return source_trends
    
    async def process_trends(self, trends: List[TrendData]) -> List[TrendData]:
        """
        Process scraped trends through the complete pipeline.
        
        Args:
            trends: Raw scraped trends
            
        Returns:
            Processed and scored trends ready for storage
        """
        self.logger.info("Starting trend processing pipeline", input_count=len(trends))
        
        # Step 1: Apply filters
        filtered_trends = self.filter_pipeline.apply_all(trends)
        self.stats["total_filtered"] = len(filtered_trends)
        
        self.logger.info(
            "Filtering completed",
            input_count=len(trends),
            output_count=len(filtered_trends),
            filtered_out=len(trends) - len(filtered_trends)
        )
        
        # Step 2: Validate trends
        validation_result = self.validator.validate_trends(filtered_trends)
        valid_trends = [
            trend for trend in filtered_trends
            if self.validator.validate_trend(trend)["is_valid"]
        ]
        self.stats["total_validated"] = len(valid_trends)
        
        self.logger.info(
            "Validation completed",
            input_count=len(filtered_trends),
            valid_count=len(valid_trends),
            invalid_count=len(filtered_trends) - len(valid_trends),
            validation_rate=f"{validation_result['validation_rate']:.2%}"
        )
        
        # Step 3: Deduplicate trends
        deduplicated_trends = self.deduplicator.deduplicate(valid_trends)
        self.stats["total_deduplicated"] = len(deduplicated_trends)
        
        self.logger.info(
            "Deduplication completed",
            input_count=len(valid_trends),
            output_count=len(deduplicated_trends),
            duplicates_removed=len(valid_trends) - len(deduplicated_trends)
        )
        
        # Step 4: Score trends
        scored_trends = self.scorer.base_scorer.score_trends(deduplicated_trends)
        
        self.logger.info(
            "Scoring completed",
            trend_count=len(scored_trends),
            avg_score=sum(t.raw_data.get('calculated_score', 0) for t in scored_trends) / len(scored_trends) if scored_trends else 0
        )
        
        return scored_trends
    
    async def store_trends(self, trends: List[TrendData]) -> int:
        """
        Store processed trends in the database.
        
        Args:
            trends: Processed trends to store
            
        Returns:
            Number of trends successfully stored
        """
        self.logger.info("Starting trend storage", trend_count=len(trends))
        
        stored_count = 0
        
        for trend in trends:
            try:
                # Check if trend already exists
                existing = await self.trend_repository.get_by_keyword_and_region(
                    trend.keyword, trend.region
                )
                
                if existing:
                    # Update existing trend if new score is higher
                    new_score = trend.raw_data.get('calculated_score', 0)
                    existing_score = existing.get('score', 0)
                    
                    if new_score > existing_score:
                        await self.trend_repository.update(existing['id'], {
                            'score': new_score,
                            'search_volume': trend.search_volume,
                            'growth_rate': trend.growth_rate,
                            'raw_data': trend.raw_data,
                            'updated_at': datetime.utcnow()
                        })
                        stored_count += 1
                        
                        self.logger.debug(
                            "Updated existing trend",
                            keyword=trend.keyword,
                            old_score=existing_score,
                            new_score=new_score
                        )
                else:
                    # Create new trend
                    trend_data = {
                        'keyword': trend.keyword,
                        'slug': trend.generate_slug(),
                        'status': 'pending',
                        'region': trend.region,
                        'category': trend.category,
                        'search_volume': trend.search_volume,
                        'growth_rate': trend.growth_rate,
                        'score': trend.raw_data.get('calculated_score', 0),
                        'source': trend.source,
                        'raw_data': trend.raw_data or {},
                        'created_at': datetime.utcnow()
                    }
                    
                    await self.trend_repository.create(trend_data)
                    stored_count += 1
                    
                    self.logger.debug(
                        "Created new trend",
                        keyword=trend.keyword,
                        score=trend_data['score']
                    )
            
            except Exception as e:
                error_msg = f"Failed to store trend '{trend.keyword}': {str(e)}"
                self.logger.error("Trend storage failed", keyword=trend.keyword, error=str(e))
                self.stats["errors"].append(error_msg)
        
        self.stats["total_stored"] = stored_count
        
        self.logger.info(
            "Trend storage completed",
            input_count=len(trends),
            stored_count=stored_count,
            skipped_count=len(trends) - stored_count
        )
        
        return stored_count
    
    async def run_full_pipeline(
        self,
        regions: List[str] = None,
        categories: List[str] = None,
        max_trends_per_source: int = 50
    ) -> Dict[str, Any]:
        """
        Run the complete trend scraping and processing pipeline.
        
        Args:
            regions: List of regions to scrape
            categories: List of categories to scrape
            max_trends_per_source: Maximum trends per source
            
        Returns:
            Pipeline execution summary
        """
        start_time = datetime.utcnow()
        
        self.logger.info("Starting full trend pipeline")
        
        try:
            # Step 1: Scrape trends
            raw_trends = await self.scrape_all_sources(regions, categories, max_trends_per_source)
            
            if not raw_trends:
                self.logger.warning("No trends scraped from any source")
                return self._generate_summary(start_time, success=False)
            
            # Step 2: Process trends
            processed_trends = await self.process_trends(raw_trends)
            
            if not processed_trends:
                self.logger.warning("No trends survived processing pipeline")
                return self._generate_summary(start_time, success=False)
            
            # Step 3: Store trends
            stored_count = await self.store_trends(processed_trends)
            
            # Generate summary
            summary = self._generate_summary(start_time, success=True)
            
            self.logger.info(
                "Full pipeline completed successfully",
                duration_seconds=summary["duration_seconds"],
                trends_stored=stored_count
            )
            
            return summary
        
        except Exception as e:
            self.logger.error("Pipeline execution failed", error=str(e))
            self.stats["errors"].append(f"Pipeline failure: {str(e)}")
            return self._generate_summary(start_time, success=False)
    
    def _generate_summary(self, start_time: datetime, success: bool) -> Dict[str, Any]:
        """Generate pipeline execution summary."""
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        
        return {
            "success": success,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "statistics": dict(self.stats),
            "pipeline_efficiency": {
                "scraping_rate": self.stats["total_scraped"] / duration if duration > 0 else 0,
                "processing_rate": self.stats["total_filtered"] / duration if duration > 0 else 0,
                "storage_rate": self.stats["total_stored"] / duration if duration > 0 else 0,
                "overall_efficiency": self.stats["total_stored"] / max(1, self.stats["total_scraped"]) if self.stats["total_scraped"] > 0 else 0
            }
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get current orchestrator statistics."""
        return {
            "stats": dict(self.stats),
            "scraper_count": len(self.scrapers),
            "filter_count": len(self.filter_pipeline.filters),
            "config": self.config
        }
