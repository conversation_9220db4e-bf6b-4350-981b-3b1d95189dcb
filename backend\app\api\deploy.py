"""
Deployment API endpoints.

Handles deployment operations, status monitoring, and deployment-related
management for the content platform.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field
from datetime import datetime
import structlog

from ..dependencies import (
    get_deployment_repository,
    get_trend_repository,
    get_content_repository,
    get_current_active_user,
    require_editor,
    get_pagination_params,
    PaginationParams
)
from database.models.deployment_model import DeploymentRepository
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository

logger = structlog.get_logger()

router = APIRouter(prefix="/deploy")


class DeploymentResponse(BaseModel):
    """Deployment response model."""
    id: str
    trend_id: str
    content_id: Optional[str]
    status: str
    coolify_deployment_uuid: Optional[str]
    build_log: Optional[str]
    deploy_url: Optional[str]
    error_message: Optional[str]
    progress: int
    build_duration: Optional[int]
    created_at: datetime
    deployed_at: Optional[datetime]


class DeploymentListResponse(BaseModel):
    """Paginated deployment list response."""
    data: List[DeploymentResponse]
    pagination: Dict[str, Any]


class DeploymentRequest(BaseModel):
    """Deployment trigger request."""
    trend_id: str
    force_redeploy: bool = Field(default=False, description="Force redeploy even if already deployed")


class BulkDeploymentRequest(BaseModel):
    """Bulk deployment request."""
    trend_ids: List[str] = Field(..., min_items=1, max_items=20)
    force_redeploy: bool = Field(default=False)


@router.get("/", response_model=DeploymentListResponse)
async def list_deployments(
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    trend_id: Optional[str] = Query(None, description="Filter by trend ID"),
    pagination: PaginationParams = Depends(get_pagination_params),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repository)
):
    """List deployments with filtering and pagination."""
    logger.info(
        "List deployments request",
        user_id=current_user["id"],
        status_filter=status_filter,
        trend_id=trend_id,
        page=pagination.page,
        page_size=pagination.page_size
    )
    
    try:
        filters = {}
        if status_filter:
            filters["status"] = status_filter
        if trend_id:
            filters["trend_id"] = trend_id
        
        result = await deployment_repo.list_with_pagination(
            page=pagination.page,
            page_size=pagination.page_size,
            filters=filters,
            order_by="created_at DESC"
        )
        
        return DeploymentListResponse(
            data=[DeploymentResponse(**deployment) for deployment in result["data"]],
            pagination=result["pagination"]
        )
    
    except Exception as e:
        logger.error("Failed to list deployments", error=str(e), user_id=current_user["id"])
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployments"
        )


@router.get("/{deployment_id}", response_model=DeploymentResponse)
async def get_deployment(
    deployment_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repository)
):
    """Get specific deployment by ID."""
    logger.info("Get deployment", deployment_id=deployment_id, user_id=current_user["id"])
    
    try:
        deployment = await deployment_repo.get_by_id(deployment_id)
        if not deployment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deployment not found"
            )
        
        return DeploymentResponse(**deployment)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get deployment", error=str(e), deployment_id=deployment_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployment"
        )


@router.get("/trend/{trend_id}", response_model=List[DeploymentResponse])
async def get_deployments_by_trend(
    trend_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repository)
):
    """Get all deployments for a specific trend."""
    logger.info("Get deployments by trend", trend_id=trend_id, user_id=current_user["id"])
    
    try:
        deployments = await deployment_repo.get_by_trend_id(trend_id)
        return [DeploymentResponse(**deployment) for deployment in deployments]
    
    except Exception as e:
        logger.error("Failed to get deployments by trend", error=str(e), trend_id=trend_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployments"
        )


@router.post("/trigger", response_model=Dict[str, Any])
async def trigger_deployment(
    request: DeploymentRequest,
    current_user: Dict[str, Any] = Depends(require_editor()),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repository),
    trend_repo: TrendRepository = Depends(get_trend_repository),
    content_repo: ContentRepository = Depends(get_content_repository)
):
    """Trigger deployment for a specific trend."""
    logger.info(
        "Trigger deployment",
        trend_id=request.trend_id,
        force_redeploy=request.force_redeploy,
        user_id=current_user["id"]
    )
    
    try:
        # Verify trend exists and has content
        trend = await trend_repo.get_by_id(request.trend_id)
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        if trend["status"] != "approved":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Trend must be approved before deployment"
            )
        
        # Check if content exists
        content = await content_repo.get_by_trend_id(request.trend_id)
        if not content:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Content must be generated before deployment"
            )
        
        # Check for existing successful deployment
        if not request.force_redeploy:
            existing_deployments = await deployment_repo.get_by_trend_id(request.trend_id)
            successful_deployment = next(
                (d for d in existing_deployments if d["status"] == "success"),
                None
            )
            if successful_deployment:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Trend already deployed successfully. Use force_redeploy=true to redeploy."
                )
        
        # Create deployment record
        deployment_data = {
            "trend_id": request.trend_id,
            "content_id": content["id"],
            "status": "pending",
            "progress": 0,
            "triggered_by": current_user["id"]
        }
        
        deployment_id = await deployment_repo.create(deployment_data)
        
        # TODO: Trigger actual deployment task with Coolify
        # For now, return a placeholder response
        task_id = f"deploy_{deployment_id}_{datetime.utcnow().timestamp()}"
        
        logger.info(
            "Deployment task created",
            deployment_id=deployment_id,
            task_id=task_id,
            trend_id=request.trend_id,
            user_id=current_user["id"]
        )
        
        return {
            "deployment_id": deployment_id,
            "task_id": task_id,
            "status": "started",
            "trend_id": request.trend_id,
            "message": "Deployment task has been started"
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(
            "Failed to trigger deployment",
            error=str(e),
            trend_id=request.trend_id,
            user_id=current_user["id"]
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start deployment"
        )


@router.post("/bulk", response_model=Dict[str, Any])
async def trigger_bulk_deployment(
    request: BulkDeploymentRequest,
    current_user: Dict[str, Any] = Depends(require_editor()),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repository),
    trend_repo: TrendRepository = Depends(get_trend_repository),
    content_repo: ContentRepository = Depends(get_content_repository)
):
    """Trigger deployment for multiple trends."""
    logger.info(
        "Trigger bulk deployment",
        trend_ids=request.trend_ids,
        count=len(request.trend_ids),
        force_redeploy=request.force_redeploy,
        user_id=current_user["id"]
    )
    
    deployment_results = []
    successful_count = 0
    failed_count = 0
    
    for trend_id in request.trend_ids:
        try:
            # Create individual deployment request
            individual_request = DeploymentRequest(
                trend_id=trend_id,
                force_redeploy=request.force_redeploy
            )
            
            # Trigger deployment (reuse single deployment logic)
            result = await trigger_deployment(
                individual_request,
                current_user,
                deployment_repo,
                trend_repo,
                content_repo
            )
            
            deployment_results.append({
                "trend_id": trend_id,
                "status": "started",
                "deployment_id": result["deployment_id"],
                "task_id": result["task_id"]
            })
            successful_count += 1
            
        except HTTPException as e:
            deployment_results.append({
                "trend_id": trend_id,
                "status": "failed",
                "error": e.detail
            })
            failed_count += 1
        except Exception as e:
            deployment_results.append({
                "trend_id": trend_id,
                "status": "failed",
                "error": str(e)
            })
            failed_count += 1
    
    logger.info(
        "Bulk deployment completed",
        successful_count=successful_count,
        failed_count=failed_count,
        total_count=len(request.trend_ids),
        user_id=current_user["id"]
    )
    
    return {
        "total_count": len(request.trend_ids),
        "successful_count": successful_count,
        "failed_count": failed_count,
        "results": deployment_results,
        "message": f"Started {successful_count} deployments, {failed_count} failed"
    }


@router.post("/{deployment_id}/cancel")
async def cancel_deployment(
    deployment_id: str,
    current_user: Dict[str, Any] = Depends(require_editor()),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repository)
):
    """Cancel a pending or running deployment."""
    logger.info("Cancel deployment", deployment_id=deployment_id, user_id=current_user["id"])
    
    try:
        # Verify deployment exists
        deployment = await deployment_repo.get_by_id(deployment_id)
        if not deployment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deployment not found"
            )
        
        # Check if deployment can be cancelled
        if deployment["status"] not in ["pending", "building"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot cancel deployment with status: {deployment['status']}"
            )
        
        # Update deployment status
        success = await deployment_repo.update(deployment_id, {
            "status": "cancelled",
            "error_message": f"Cancelled by user {current_user['id']}"
        })
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to cancel deployment"
            )
        
        # TODO: Cancel actual deployment task in Coolify
        
        logger.info("Deployment cancelled", deployment_id=deployment_id, user_id=current_user["id"])
        return {"message": "Deployment cancelled successfully"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to cancel deployment", error=str(e), deployment_id=deployment_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cancel deployment"
        )


@router.get("/stats/overview", response_model=Dict[str, Any])
async def get_deployment_stats(
    current_user: Dict[str, Any] = Depends(require_editor()),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repository)
):
    """Get deployment statistics overview."""
    logger.info("Get deployment stats", user_id=current_user["id"])
    
    try:
        # TODO: Implement actual statistics calculation
        # For now, return placeholder data
        stats = {
            "total_deployments": 0,
            "successful_deployments": 0,
            "failed_deployments": 0,
            "pending_deployments": 0,
            "avg_build_duration": 0,
            "deployments_by_status": {},
            "recent_deployments_count": 0
        }
        
        return stats
    
    except Exception as e:
        logger.error("Failed to get deployment stats", error=str(e), user_id=current_user["id"])
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve deployment statistics"
        )
