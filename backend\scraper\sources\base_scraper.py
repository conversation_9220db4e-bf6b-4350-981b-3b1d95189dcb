"""
Base scraper class and data models for trend scraping.

Defines the common interface that all trend scrapers must implement
and the data structures used throughout the scraping system.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
import structlog

logger = structlog.get_logger()


class TrendSource(Enum):
    """Enumeration of supported trend sources."""
    GOOGLE_TRENDS = "google_trends"
    TRENDS24 = "trends24"
    CUSTOM = "custom"


@dataclass
class TrendData:
    """Data structure for trend information."""
    keyword: str
    search_volume: Optional[int] = None
    growth_rate: Optional[float] = None
    region: str = "US"
    category: str = "General"
    source: str = "unknown"
    timestamp: datetime = None
    raw_data: Dict[str, Any] = None
    confidence_score: float = 1.0
    
    def __post_init__(self):
        """Initialize default values after object creation."""
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()
        if self.raw_data is None:
            self.raw_data = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for database storage."""
        data = asdict(self)
        # Convert datetime to ISO string for JSON serialization
        if isinstance(data['timestamp'], datetime):
            data['timestamp'] = data['timestamp'].isoformat()
        return data
    
    def generate_slug(self) -> str:
        """Generate URL-friendly slug from keyword."""
        import re
        # Convert to lowercase and replace spaces/special chars with hyphens
        slug = re.sub(r'[^\w\s-]', '', self.keyword.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')


class BaseScraper(ABC):
    """Abstract base class for all trend scrapers."""
    
    def __init__(self, source_name: str, config: Dict[str, Any] = None):
        self.source_name = source_name
        self.config = config or {}
        self.logger = structlog.get_logger().bind(scraper=source_name)
        self._session = None
    
    @abstractmethod
    async def scrape_trends(self, region: str, category: str = None) -> List[TrendData]:
        """
        Scrape trends for a specific region and optional category.
        
        Args:
            region: Region code (e.g., 'US', 'UK', 'CA')
            category: Optional category filter
            
        Returns:
            List of TrendData objects
        """
        pass
    
    @abstractmethod
    def validate_data(self, data: TrendData) -> bool:
        """
        Validate scraped trend data.
        
        Args:
            data: TrendData object to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        pass
    
    async def setup(self):
        """Setup method called before scraping begins."""
        self.logger.info("Setting up scraper", source=self.source_name)
    
    async def cleanup(self):
        """Cleanup method called after scraping completes."""
        self.logger.info("Cleaning up scraper", source=self.source_name)
        if self._session:
            await self._session.close()
    
    def get_supported_regions(self) -> List[str]:
        """Get list of supported regions for this scraper."""
        return ["US", "UK", "CA", "AU"]  # Default regions
    
    def get_supported_categories(self) -> List[str]:
        """Get list of supported categories for this scraper."""
        return ["Technology", "Health", "Entertainment", "Business", "Science"]
    
    async def test_connection(self) -> bool:
        """Test if the scraper can connect to its data source."""
        try:
            # Default implementation - subclasses should override
            self.logger.info("Testing connection", source=self.source_name)
            return True
        except Exception as e:
            self.logger.error("Connection test failed", source=self.source_name, error=str(e))
            return False
    
    def get_rate_limit(self) -> Dict[str, int]:
        """Get rate limiting configuration for this scraper."""
        return {
            "requests_per_minute": 10,
            "requests_per_hour": 100,
            "burst_limit": 5
        }
    
    def normalize_keyword(self, keyword: str) -> str:
        """Normalize keyword for consistency."""
        if not keyword:
            return ""
        
        # Basic normalization
        normalized = keyword.strip()
        normalized = ' '.join(normalized.split())  # Remove extra whitespace
        
        return normalized
    
    def extract_metadata(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract metadata from raw scraping data."""
        return {
            "scraper_version": "1.0",
            "scrape_timestamp": datetime.utcnow().isoformat(),
            "source": self.source_name,
            "data_quality": self._assess_data_quality(raw_data)
        }
    
    def _assess_data_quality(self, raw_data: Dict[str, Any]) -> str:
        """Assess the quality of scraped data."""
        if not raw_data:
            return "poor"
        
        # Simple quality assessment based on data completeness
        required_fields = ["keyword"]
        optional_fields = ["search_volume", "growth_rate"]
        
        has_required = all(field in raw_data for field in required_fields)
        has_optional = sum(1 for field in optional_fields if field in raw_data)
        
        if not has_required:
            return "poor"
        elif has_optional >= len(optional_fields) // 2:
            return "good"
        else:
            return "fair"
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.setup()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()


class ScraperError(Exception):
    """Base exception for scraper-related errors."""
    pass


class RateLimitError(ScraperError):
    """Exception raised when rate limits are exceeded."""
    pass


class DataValidationError(ScraperError):
    """Exception raised when scraped data fails validation."""
    pass


class ConnectionError(ScraperError):
    """Exception raised when scraper cannot connect to data source."""
    pass
