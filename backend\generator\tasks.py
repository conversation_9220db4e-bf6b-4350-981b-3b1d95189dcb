"""
Celery tasks for content generation operations.

Provides asynchronous task execution for content generation, site building,
and related operations using Celery distributed task queue.
"""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime
from celery import Celery
import structlog

from .core import ContentOrchestrator
from database.connection import get_database_pool
from database.models.content_model import ContentRepository
from database.models.trend_model import TrendRepository
from app.config import settings

logger = structlog.get_logger()

# Use the same Celery app from scraper tasks
from scraper.tasks import celery_app


async def get_repositories():
    """Get repository instances."""
    pool = await get_database_pool()
    content_repo = ContentRepository(pool)
    trend_repo = TrendRepository(pool)
    return content_repo, trend_repo


@celery_app.task(bind=True, name='generator.tasks.generate_content_task')
def generate_content_task(
    self,
    trend_id: str,
    regenerate_sections: List[str] = None,
    force_regenerate: bool = False
):
    """
    Celery task for generating content for a single trend.
    
    Args:
        trend_id: ID of the trend to generate content for
        regenerate_sections: Specific sections to regenerate
        force_regenerate: Whether to force regeneration
    """
    task_id = self.request.id
    logger.info("Starting content generation task", task_id=task_id, trend_id=trend_id)
    
    try:
        result = asyncio.run(_run_content_generation(
            trend_id, regenerate_sections, force_regenerate
        ))
        
        logger.info(
            "Content generation task completed",
            task_id=task_id,
            trend_id=trend_id,
            success=result["success"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Content generation task failed", task_id=task_id, trend_id=trend_id, error=str(e))
        raise


async def _run_content_generation(
    trend_id: str,
    regenerate_sections: List[str] = None,
    force_regenerate: bool = False
) -> Dict[str, Any]:
    """Run content generation asynchronously."""
    content_repo, trend_repo = await get_repositories()
    orchestrator = ContentOrchestrator(content_repo, trend_repo)
    
    try:
        result = await orchestrator.generate_content_for_trend(
            trend_id=trend_id,
            regenerate_sections=regenerate_sections,
            force_regenerate=force_regenerate
        )
        return result
    finally:
        await orchestrator.close()


@celery_app.task(bind=True, name='generator.tasks.batch_generate_content_task')
def batch_generate_content_task(
    self,
    trend_ids: List[str],
    max_concurrent: int = 3
):
    """
    Celery task for batch content generation.
    
    Args:
        trend_ids: List of trend IDs to process
        max_concurrent: Maximum concurrent generations
    """
    task_id = self.request.id
    logger.info("Starting batch content generation", task_id=task_id, trend_count=len(trend_ids))
    
    try:
        result = asyncio.run(_run_batch_content_generation(trend_ids, max_concurrent))
        
        logger.info(
            "Batch content generation completed",
            task_id=task_id,
            total=result["total_trends"],
            successful=result["successful"],
            failed=result["failed"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Batch content generation failed", task_id=task_id, error=str(e))
        raise


async def _run_batch_content_generation(
    trend_ids: List[str],
    max_concurrent: int = 3
) -> Dict[str, Any]:
    """Run batch content generation asynchronously."""
    content_repo, trend_repo = await get_repositories()
    orchestrator = ContentOrchestrator(content_repo, trend_repo)
    
    try:
        result = await orchestrator.batch_generate_content(
            trend_ids=trend_ids,
            max_concurrent=max_concurrent
        )
        return result
    finally:
        await orchestrator.close()


@celery_app.task(bind=True, name='generator.tasks.generate_content_for_approved_trends')
def generate_content_for_approved_trends(self, limit: int = 10):
    """
    Generate content for recently approved trends.
    
    Args:
        limit: Maximum number of trends to process
    """
    task_id = self.request.id
    logger.info("Starting content generation for approved trends", task_id=task_id, limit=limit)
    
    try:
        result = asyncio.run(_generate_for_approved_trends(limit))
        
        logger.info(
            "Approved trends content generation completed",
            task_id=task_id,
            processed=result["processed_count"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Approved trends content generation failed", task_id=task_id, error=str(e))
        raise


async def _generate_for_approved_trends(limit: int) -> Dict[str, Any]:
    """Generate content for approved trends that don't have content yet."""
    content_repo, trend_repo = await get_repositories()
    orchestrator = ContentOrchestrator(content_repo, trend_repo)
    
    try:
        # Get approved trends without content
        approved_trends = await trend_repo.list_with_pagination(
            filters={"status": "approved"},
            page_size=limit,
            order_by="created_at DESC"
        )
        
        # Filter out trends that already have content
        trends_needing_content = []
        for trend in approved_trends["data"]:
            existing_content = await content_repo.get_by_trend_id(trend["id"])
            if not existing_content:
                trends_needing_content.append(trend["id"])
        
        if not trends_needing_content:
            return {
                "processed_count": 0,
                "message": "No approved trends need content generation"
            }
        
        # Generate content for trends
        batch_result = await orchestrator.batch_generate_content(
            trend_ids=trends_needing_content,
            max_concurrent=3
        )
        
        return {
            "processed_count": len(trends_needing_content),
            "successful": batch_result["successful"],
            "failed": batch_result["failed"],
            "batch_result": batch_result
        }
    
    finally:
        await orchestrator.close()


@celery_app.task(bind=True, name='generator.tasks.regenerate_content_task')
def regenerate_content_task(
    self,
    content_id: str,
    sections: List[str] = None
):
    """
    Regenerate specific sections of existing content.
    
    Args:
        content_id: ID of the content to regenerate
        sections: Sections to regenerate (title, body, image, code)
    """
    task_id = self.request.id
    logger.info("Starting content regeneration", task_id=task_id, content_id=content_id)
    
    try:
        result = asyncio.run(_regenerate_content(content_id, sections))
        
        logger.info(
            "Content regeneration completed",
            task_id=task_id,
            content_id=content_id,
            success=result["success"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Content regeneration failed", task_id=task_id, content_id=content_id, error=str(e))
        raise


async def _regenerate_content(content_id: str, sections: List[str] = None) -> Dict[str, Any]:
    """Regenerate content sections."""
    content_repo, trend_repo = await get_repositories()
    orchestrator = ContentOrchestrator(content_repo, trend_repo)
    
    try:
        # Get content and associated trend
        content = await content_repo.get_by_id(content_id)
        if not content:
            return {
                "success": False,
                "error": f"Content not found: {content_id}"
            }
        
        trend_id = content["trend_id"]
        
        # Regenerate content
        result = await orchestrator.generate_content_for_trend(
            trend_id=trend_id,
            regenerate_sections=sections,
            force_regenerate=True
        )
        
        return result
    
    finally:
        await orchestrator.close()


@celery_app.task(bind=True, name='generator.tasks.cleanup_old_content_task')
def cleanup_old_content_task(self, days_to_keep: int = 30):
    """
    Clean up old generated content and sites.
    
    Args:
        days_to_keep: Number of days of content to keep
    """
    task_id = self.request.id
    logger.info("Starting content cleanup", task_id=task_id, days_to_keep=days_to_keep)
    
    try:
        result = asyncio.run(_cleanup_old_content(days_to_keep))
        
        logger.info(
            "Content cleanup completed",
            task_id=task_id,
            content_cleaned=result["content_cleaned"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Content cleanup failed", task_id=task_id, error=str(e))
        raise


async def _cleanup_old_content(days_to_keep: int) -> Dict[str, Any]:
    """Clean up old content."""
    content_repo, trend_repo = await get_repositories()
    orchestrator = ContentOrchestrator(content_repo, trend_repo)
    
    try:
        result = await orchestrator.cleanup_old_content(days_to_keep)
        return result
    finally:
        await orchestrator.close()


@celery_app.task(bind=True, name='generator.tasks.rebuild_static_sites_task')
def rebuild_static_sites_task(self, content_ids: List[str] = None):
    """
    Rebuild static sites for existing content.
    
    Args:
        content_ids: Specific content IDs to rebuild, or None for all
    """
    task_id = self.request.id
    logger.info("Starting static site rebuild", task_id=task_id)
    
    try:
        result = asyncio.run(_rebuild_static_sites(content_ids))
        
        logger.info(
            "Static site rebuild completed",
            task_id=task_id,
            sites_rebuilt=result["sites_rebuilt"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Static site rebuild failed", task_id=task_id, error=str(e))
        raise


async def _rebuild_static_sites(content_ids: List[str] = None) -> Dict[str, Any]:
    """Rebuild static sites for content."""
    content_repo, trend_repo = await get_repositories()
    orchestrator = ContentOrchestrator(content_repo, trend_repo)
    
    try:
        # Get content to rebuild
        if content_ids:
            content_list = []
            for content_id in content_ids:
                content = await content_repo.get_by_id(content_id)
                if content:
                    content_list.append(content)
        else:
            # Get all content
            all_content = await content_repo.list_with_pagination(page_size=1000)
            content_list = all_content["data"]
        
        sites_rebuilt = 0
        errors = []
        
        for content in content_list:
            try:
                # Get associated trend
                trend = await trend_repo.get_by_id(content["trend_id"])
                if not trend:
                    continue
                
                # Rebuild site
                site_result = await orchestrator._generate_static_site(trend, content)
                if site_result.get("success"):
                    sites_rebuilt += 1
                else:
                    errors.append(f"Content {content['id']}: {site_result.get('error', 'Unknown error')}")
            
            except Exception as e:
                errors.append(f"Content {content['id']}: {str(e)}")
        
        return {
            "total_content": len(content_list),
            "sites_rebuilt": sites_rebuilt,
            "errors": errors,
            "success_rate": sites_rebuilt / len(content_list) if content_list else 0
        }
    
    finally:
        await orchestrator.close()


@celery_app.task(bind=True, name='generator.tasks.get_generation_stats_task')
def get_generation_stats_task(self):
    """Get content generation statistics."""
    task_id = self.request.id
    logger.info("Getting generation stats", task_id=task_id)
    
    try:
        result = asyncio.run(_get_generation_stats())
        return result
    
    except Exception as e:
        logger.error("Failed to get generation stats", task_id=task_id, error=str(e))
        raise


async def _get_generation_stats() -> Dict[str, Any]:
    """Get generation statistics."""
    content_repo, trend_repo = await get_repositories()
    orchestrator = ContentOrchestrator(content_repo, trend_repo)
    
    try:
        # Get orchestrator stats
        orchestrator_stats = await orchestrator.get_generation_stats()
        
        # Get database stats
        content_stats = await content_repo.get_content_stats()
        
        # Combine stats
        combined_stats = {
            "orchestrator": orchestrator_stats,
            "database": content_stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        return combined_stats
    
    finally:
        await orchestrator.close()


# Update Celery beat schedule to include content generation tasks
celery_app.conf.beat_schedule.update({
    'generate-content-for-approved-trends': {
        'task': 'generator.tasks.generate_content_for_approved_trends',
        'schedule': 300.0,  # Every 5 minutes
        'args': (10,)  # Process up to 10 trends
    },
    'cleanup-old-content': {
        'task': 'generator.tasks.cleanup_old_content_task',
        'schedule': 86400.0,  # Daily
        'args': (30,)  # Keep 30 days
    }
})

# Task routing for content generation
celery_app.conf.task_routes.update({
    'generator.tasks.*': {'queue': 'generator'},
})
