"""
Proxy rotation utilities for web scraping.

Provides proxy rotation functionality to distribute requests across
multiple proxy servers and avoid IP-based blocking.
"""

import random
import time
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from enum import Enum
import asyncio
import aiohttp
import structlog

logger = structlog.get_logger()


class ProxyStatus(Enum):
    """Proxy status enumeration."""
    ACTIVE = "active"
    FAILED = "failed"
    TESTING = "testing"
    DISABLED = "disabled"


@dataclass
class ProxyInfo:
    """Information about a proxy server."""
    url: str
    status: ProxyStatus = ProxyStatus.ACTIVE
    success_count: int = 0
    failure_count: int = 0
    last_used: Optional[float] = None
    last_tested: Optional[float] = None
    response_time: Optional[float] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of the proxy."""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 1.0
    
    @property
    def is_healthy(self) -> bool:
        """Check if proxy is considered healthy."""
        return (
            self.status == ProxyStatus.ACTIVE and
            self.success_rate >= 0.7 and
            self.failure_count < 10
        )


class ProxyRotator:
    """Proxy rotation manager with health checking and load balancing."""
    
    def __init__(
        self,
        proxy_list: List[str],
        rotation_strategy: str = "round_robin",
        health_check_interval: int = 300,  # 5 minutes
        max_failures: int = 5
    ):
        """
        Initialize proxy rotator.
        
        Args:
            proxy_list: List of proxy URLs (format: "host:port" or "user:pass@host:port")
            rotation_strategy: Strategy for proxy selection ("round_robin", "random", "weighted")
            health_check_interval: Interval between health checks in seconds
            max_failures: Maximum failures before disabling a proxy
        """
        self.rotation_strategy = rotation_strategy
        self.health_check_interval = health_check_interval
        self.max_failures = max_failures
        
        # Initialize proxy info objects
        self.proxies = [ProxyInfo(url=proxy) for proxy in proxy_list]
        self.current_index = 0
        
        # Health checking
        self._health_check_task = None
        self._lock = asyncio.Lock()
        
        self.logger = structlog.get_logger().bind(component="proxy_rotator")
        self.logger.info("Proxy rotator initialized", proxy_count=len(self.proxies))
    
    async def start_health_checks(self):
        """Start background health checking task."""
        if self._health_check_task is None:
            self._health_check_task = asyncio.create_task(self._health_check_loop())
            self.logger.info("Started proxy health checking")
    
    async def stop_health_checks(self):
        """Stop background health checking task."""
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
            self._health_check_task = None
            self.logger.info("Stopped proxy health checking")
    
    def get_proxy(self) -> Optional[str]:
        """
        Get the next proxy based on rotation strategy.
        
        Returns:
            Proxy URL or None if no healthy proxies available
        """
        healthy_proxies = [p for p in self.proxies if p.is_healthy]
        
        if not healthy_proxies:
            self.logger.warning("No healthy proxies available")
            return None
        
        if self.rotation_strategy == "round_robin":
            proxy = self._get_round_robin_proxy(healthy_proxies)
        elif self.rotation_strategy == "random":
            proxy = self._get_random_proxy(healthy_proxies)
        elif self.rotation_strategy == "weighted":
            proxy = self._get_weighted_proxy(healthy_proxies)
        else:
            proxy = healthy_proxies[0]  # Fallback
        
        # Update usage timestamp
        proxy.last_used = time.time()
        
        self.logger.debug("Selected proxy", proxy_url=proxy.url, strategy=self.rotation_strategy)
        return proxy.url
    
    def _get_round_robin_proxy(self, healthy_proxies: List[ProxyInfo]) -> ProxyInfo:
        """Get proxy using round-robin strategy."""
        proxy = healthy_proxies[self.current_index % len(healthy_proxies)]
        self.current_index = (self.current_index + 1) % len(healthy_proxies)
        return proxy
    
    def _get_random_proxy(self, healthy_proxies: List[ProxyInfo]) -> ProxyInfo:
        """Get proxy using random selection."""
        return random.choice(healthy_proxies)
    
    def _get_weighted_proxy(self, healthy_proxies: List[ProxyInfo]) -> ProxyInfo:
        """Get proxy using weighted selection based on success rate and response time."""
        # Calculate weights based on success rate and inverse response time
        weights = []
        for proxy in healthy_proxies:
            success_weight = proxy.success_rate
            speed_weight = 1.0 / (proxy.response_time or 1.0)  # Faster = higher weight
            combined_weight = success_weight * speed_weight
            weights.append(combined_weight)
        
        # Weighted random selection
        total_weight = sum(weights)
        if total_weight == 0:
            return random.choice(healthy_proxies)
        
        r = random.uniform(0, total_weight)
        cumulative_weight = 0
        
        for i, weight in enumerate(weights):
            cumulative_weight += weight
            if r <= cumulative_weight:
                return healthy_proxies[i]
        
        return healthy_proxies[-1]  # Fallback
    
    async def record_success(self, proxy_url: str, response_time: float = None):
        """
        Record successful use of a proxy.
        
        Args:
            proxy_url: URL of the proxy that succeeded
            response_time: Response time in seconds
        """
        async with self._lock:
            proxy = self._find_proxy(proxy_url)
            if proxy:
                proxy.success_count += 1
                if response_time:
                    # Update response time with exponential moving average
                    if proxy.response_time is None:
                        proxy.response_time = response_time
                    else:
                        proxy.response_time = 0.7 * proxy.response_time + 0.3 * response_time
                
                # Re-enable proxy if it was disabled due to failures
                if proxy.status == ProxyStatus.FAILED and proxy.success_rate >= 0.8:
                    proxy.status = ProxyStatus.ACTIVE
                    self.logger.info("Re-enabled proxy", proxy_url=proxy_url)
    
    async def record_failure(self, proxy_url: str, error: str = None):
        """
        Record failed use of a proxy.
        
        Args:
            proxy_url: URL of the proxy that failed
            error: Error message
        """
        async with self._lock:
            proxy = self._find_proxy(proxy_url)
            if proxy:
                proxy.failure_count += 1
                
                # Disable proxy if it has too many failures
                if proxy.failure_count >= self.max_failures:
                    proxy.status = ProxyStatus.FAILED
                    self.logger.warning(
                        "Disabled proxy due to failures",
                        proxy_url=proxy_url,
                        failure_count=proxy.failure_count,
                        error=error
                    )
    
    def _find_proxy(self, proxy_url: str) -> Optional[ProxyInfo]:
        """Find proxy info by URL."""
        for proxy in self.proxies:
            if proxy.url == proxy_url:
                return proxy
        return None
    
    async def _health_check_loop(self):
        """Background task for periodic health checking."""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._check_all_proxies()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Health check loop error", error=str(e))
    
    async def _check_all_proxies(self):
        """Check health of all proxies."""
        self.logger.info("Starting proxy health check")
        
        # Create health check tasks
        tasks = []
        for proxy in self.proxies:
            task = asyncio.create_task(self._check_proxy_health(proxy))
            tasks.append(task)
        
        # Wait for all health checks to complete
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Log results
        healthy_count = sum(1 for proxy in self.proxies if proxy.is_healthy)
        self.logger.info(
            "Proxy health check completed",
            total_proxies=len(self.proxies),
            healthy_proxies=healthy_count
        )
    
    async def _check_proxy_health(self, proxy: ProxyInfo):
        """Check health of a single proxy."""
        proxy.status = ProxyStatus.TESTING
        proxy.last_tested = time.time()
        
        try:
            # Test proxy with a simple HTTP request
            start_time = time.time()
            
            timeout = aiohttp.ClientTimeout(total=10)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(
                    "http://httpbin.org/ip",
                    proxy=f"http://{proxy.url}"
                ) as response:
                    if response.status == 200:
                        response_time = time.time() - start_time
                        await self.record_success(proxy.url, response_time)
                        proxy.status = ProxyStatus.ACTIVE
                    else:
                        await self.record_failure(proxy.url, f"HTTP {response.status}")
                        proxy.status = ProxyStatus.FAILED
        
        except Exception as e:
            await self.record_failure(proxy.url, str(e))
            proxy.status = ProxyStatus.FAILED
    
    def get_stats(self) -> Dict[str, Any]:
        """Get proxy rotation statistics."""
        total_proxies = len(self.proxies)
        healthy_proxies = sum(1 for p in self.proxies if p.is_healthy)
        active_proxies = sum(1 for p in self.proxies if p.status == ProxyStatus.ACTIVE)
        failed_proxies = sum(1 for p in self.proxies if p.status == ProxyStatus.FAILED)
        
        return {
            "total_proxies": total_proxies,
            "healthy_proxies": healthy_proxies,
            "active_proxies": active_proxies,
            "failed_proxies": failed_proxies,
            "rotation_strategy": self.rotation_strategy,
            "proxy_details": [
                {
                    "url": p.url,
                    "status": p.status.value,
                    "success_rate": p.success_rate,
                    "response_time": p.response_time,
                    "success_count": p.success_count,
                    "failure_count": p.failure_count
                }
                for p in self.proxies
            ]
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.start_health_checks()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop_health_checks()
