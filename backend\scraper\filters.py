"""
Filtering utilities for trend data.

Provides filtering capabilities for trends based on region, category,
keywords, and other criteria to ensure quality and relevance.
"""

import re
from typing import List, Dict, Set, Optional, Any
from dataclasses import dataclass
from enum import Enum
import structlog

from .sources.base_scraper import TrendData

logger = structlog.get_logger()


class FilterAction(Enum):
    """Filter action enumeration."""
    INCLUDE = "include"
    EXCLUDE = "exclude"
    MODIFY = "modify"


@dataclass
class FilterRule:
    """Configuration for a filter rule."""
    name: str
    action: FilterAction
    pattern: str = None
    keywords: List[str] = None
    min_length: int = None
    max_length: int = None
    priority: int = 0
    
    def __post_init__(self):
        if self.keywords is None:
            self.keywords = []


class BaseFilter:
    """Base class for trend filters."""
    
    def __init__(self, name: str, enabled: bool = True):
        self.name = name
        self.enabled = enabled
        self.logger = structlog.get_logger().bind(filter=name)
    
    def apply(self, trends: List[TrendData]) -> List[TrendData]:
        """
        Apply filter to list of trends.
        
        Args:
            trends: List of trend data to filter
            
        Returns:
            Filtered list of trends
        """
        if not self.enabled:
            return trends
        
        filtered_trends = []
        for trend in trends:
            if self.should_include(trend):
                modified_trend = self.modify_trend(trend)
                filtered_trends.append(modified_trend)
        
        self.logger.info(
            "Filter applied",
            input_count=len(trends),
            output_count=len(filtered_trends),
            filtered_out=len(trends) - len(filtered_trends)
        )
        
        return filtered_trends
    
    def should_include(self, trend: TrendData) -> bool:
        """
        Check if trend should be included.
        
        Args:
            trend: Trend data to check
            
        Returns:
            True if trend should be included
        """
        return True
    
    def modify_trend(self, trend: TrendData) -> TrendData:
        """
        Modify trend data if needed.
        
        Args:
            trend: Original trend data
            
        Returns:
            Modified trend data
        """
        return trend


class RegionFilter(BaseFilter):
    """Filter trends based on region/geographic criteria."""
    
    def __init__(self, allowed_regions: List[str] = None, blocked_regions: List[str] = None):
        super().__init__("region_filter")
        self.allowed_regions = set(allowed_regions or [])
        self.blocked_regions = set(blocked_regions or [])
    
    def should_include(self, trend: TrendData) -> bool:
        """Check if trend's region is allowed."""
        # If allowed regions specified, trend must be in allowed list
        if self.allowed_regions and trend.region not in self.allowed_regions:
            return False
        
        # If blocked regions specified, trend must not be in blocked list
        if self.blocked_regions and trend.region in self.blocked_regions:
            return False
        
        return True


class CategoryFilter(BaseFilter):
    """Filter trends based on category."""
    
    def __init__(self, allowed_categories: List[str] = None, blocked_categories: List[str] = None):
        super().__init__("category_filter")
        self.allowed_categories = set(allowed_categories or [])
        self.blocked_categories = set(blocked_categories or [])
    
    def should_include(self, trend: TrendData) -> bool:
        """Check if trend's category is allowed."""
        # If allowed categories specified, trend must be in allowed list
        if self.allowed_categories and trend.category not in self.allowed_categories:
            return False
        
        # If blocked categories specified, trend must not be in blocked list
        if self.blocked_categories and trend.category in self.blocked_categories:
            return False
        
        return True


class KeywordFilter(BaseFilter):
    """Filter trends based on keyword patterns and rules."""
    
    def __init__(self, rules: List[FilterRule] = None):
        super().__init__("keyword_filter")
        self.rules = sorted(rules or [], key=lambda r: r.priority, reverse=True)
        
        # Compile regex patterns for performance
        self._compiled_patterns = {}
        for rule in self.rules:
            if rule.pattern:
                try:
                    self._compiled_patterns[rule.name] = re.compile(rule.pattern, re.IGNORECASE)
                except re.error as e:
                    self.logger.error("Invalid regex pattern", rule=rule.name, pattern=rule.pattern, error=str(e))
    
    def should_include(self, trend: TrendData) -> bool:
        """Check if trend keyword passes all filter rules."""
        keyword = trend.keyword.lower()
        
        for rule in self.rules:
            if rule.action == FilterAction.EXCLUDE:
                if self._matches_rule(keyword, rule):
                    self.logger.debug("Trend excluded by rule", keyword=trend.keyword, rule=rule.name)
                    return False
            elif rule.action == FilterAction.INCLUDE:
                if not self._matches_rule(keyword, rule):
                    self.logger.debug("Trend not included by rule", keyword=trend.keyword, rule=rule.name)
                    return False
        
        return True
    
    def modify_trend(self, trend: TrendData) -> TrendData:
        """Apply modification rules to trend."""
        modified_keyword = trend.keyword
        
        for rule in self.rules:
            if rule.action == FilterAction.MODIFY and self._matches_rule(modified_keyword.lower(), rule):
                # Apply modifications (this is a simple example)
                modified_keyword = modified_keyword.strip()
                break
        
        if modified_keyword != trend.keyword:
            trend.keyword = modified_keyword
        
        return trend
    
    def _matches_rule(self, keyword: str, rule: FilterRule) -> bool:
        """Check if keyword matches a filter rule."""
        # Check length constraints
        if rule.min_length and len(keyword) < rule.min_length:
            return False
        if rule.max_length and len(keyword) > rule.max_length:
            return False
        
        # Check keyword list
        if rule.keywords:
            keyword_lower = keyword.lower()
            for blocked_keyword in rule.keywords:
                if blocked_keyword.lower() in keyword_lower:
                    return True
        
        # Check regex pattern
        if rule.pattern and rule.name in self._compiled_patterns:
            pattern = self._compiled_patterns[rule.name]
            if pattern.search(keyword):
                return True
        
        return False


class QualityFilter(BaseFilter):
    """Filter trends based on data quality metrics."""
    
    def __init__(
        self,
        min_confidence: float = 0.5,
        require_search_volume: bool = False,
        min_search_volume: int = None,
        max_search_volume: int = None
    ):
        super().__init__("quality_filter")
        self.min_confidence = min_confidence
        self.require_search_volume = require_search_volume
        self.min_search_volume = min_search_volume
        self.max_search_volume = max_search_volume
    
    def should_include(self, trend: TrendData) -> bool:
        """Check if trend meets quality criteria."""
        # Check confidence score
        if trend.confidence_score < self.min_confidence:
            return False
        
        # Check search volume requirements
        if self.require_search_volume and trend.search_volume is None:
            return False
        
        if trend.search_volume is not None:
            if self.min_search_volume and trend.search_volume < self.min_search_volume:
                return False
            if self.max_search_volume and trend.search_volume > self.max_search_volume:
                return False
        
        return True


class DuplicateFilter(BaseFilter):
    """Filter out duplicate trends based on keyword similarity."""
    
    def __init__(self, similarity_threshold: float = 0.8):
        super().__init__("duplicate_filter")
        self.similarity_threshold = similarity_threshold
    
    def apply(self, trends: List[TrendData]) -> List[TrendData]:
        """Remove duplicate trends based on keyword similarity."""
        if not self.enabled or not trends:
            return trends
        
        unique_trends = []
        seen_keywords = set()
        
        for trend in trends:
            normalized_keyword = self._normalize_keyword(trend.keyword)
            
            # Check for exact duplicates first
            if normalized_keyword in seen_keywords:
                continue
            
            # Check for similar keywords
            is_duplicate = False
            for existing_keyword in seen_keywords:
                if self._calculate_similarity(normalized_keyword, existing_keyword) >= self.similarity_threshold:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_trends.append(trend)
                seen_keywords.add(normalized_keyword)
        
        self.logger.info(
            "Duplicate filter applied",
            input_count=len(trends),
            output_count=len(unique_trends),
            duplicates_removed=len(trends) - len(unique_trends)
        )
        
        return unique_trends
    
    def _normalize_keyword(self, keyword: str) -> str:
        """Normalize keyword for comparison."""
        # Convert to lowercase and remove extra whitespace
        normalized = re.sub(r'\s+', ' ', keyword.lower().strip())
        
        # Remove common punctuation
        normalized = re.sub(r'[^\w\s]', '', normalized)
        
        return normalized
    
    def _calculate_similarity(self, keyword1: str, keyword2: str) -> float:
        """Calculate similarity between two keywords using Jaccard similarity."""
        # Split into words
        words1 = set(keyword1.split())
        words2 = set(keyword2.split())
        
        # Calculate Jaccard similarity
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0


class FilterPipeline:
    """Pipeline for applying multiple filters in sequence."""
    
    def __init__(self, filters: List[BaseFilter] = None):
        self.filters = filters or []
        self.logger = structlog.get_logger().bind(component="filter_pipeline")
    
    def add_filter(self, filter_instance: BaseFilter):
        """Add a filter to the pipeline."""
        self.filters.append(filter_instance)
        self.logger.info("Filter added to pipeline", filter_name=filter_instance.name)
    
    def remove_filter(self, filter_name: str):
        """Remove a filter from the pipeline."""
        self.filters = [f for f in self.filters if f.name != filter_name]
        self.logger.info("Filter removed from pipeline", filter_name=filter_name)
    
    def apply_all(self, trends: List[TrendData]) -> List[TrendData]:
        """Apply all filters in the pipeline."""
        self.logger.info("Starting filter pipeline", input_count=len(trends), filter_count=len(self.filters))
        
        filtered_trends = trends
        
        for filter_instance in self.filters:
            if filter_instance.enabled:
                filtered_trends = filter_instance.apply(filtered_trends)
                self.logger.debug(
                    "Filter applied",
                    filter_name=filter_instance.name,
                    remaining_count=len(filtered_trends)
                )
        
        self.logger.info(
            "Filter pipeline completed",
            input_count=len(trends),
            output_count=len(filtered_trends),
            total_filtered=len(trends) - len(filtered_trends)
        )
        
        return filtered_trends
    
    def get_stats(self) -> Dict[str, Any]:
        """Get filter pipeline statistics."""
        return {
            "total_filters": len(self.filters),
            "enabled_filters": sum(1 for f in self.filters if f.enabled),
            "filter_names": [f.name for f in self.filters if f.enabled]
        }
