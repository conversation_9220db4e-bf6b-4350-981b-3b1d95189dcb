"""
Trends24.in scraper implementation.

Scrapes trending topics from trends24.in using web scraping
with BeautifulSoup and aiohttp.
"""

import asyncio
import random
from typing import List, Dict, Optional, Any
from datetime import datetime
import aiohttp
from bs4 import BeautifulSoup
import structlog

from .base_scraper import BaseScraper, TrendData, RateLimitError, ConnectionError
from ..utils.rate_limiting import RateLimiter

logger = structlog.get_logger()


class Trends24Scraper(BaseScraper):
    """Trends24.in scraper for Twitter trending topics."""
    
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("trends24", config)
        
        # Configuration
        self.base_url = "https://trends24.in"
        self.rate_limit_rpm = self.config.get("rate_limit_rpm", 30)
        self.timeout = self.config.get("timeout", 20)
        
        # Initialize rate limiter
        self.rate_limiter = RateLimiter(requests_per_minute=self.rate_limit_rpm)
        
        # Country/region mapping for trends24.in
        self.region_mapping = {
            "US": "united-states",
            "UK": "united-kingdom", 
            "CA": "canada",
            "AU": "australia"
        }
    
    async def setup(self):
        """Setup HTTP session."""
        await super().setup()
        
        # Create HTTP session with timeout
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        # Test connection
        if not await self.test_connection():
            raise ConnectionError("Failed to connect to Trends24")
        
        self.logger.info("Trends24 scraper setup complete")
    
    async def cleanup(self):
        """Cleanup HTTP session."""
        if hasattr(self, 'session') and self.session:
            await self.session.close()
        await super().cleanup()
    
    async def scrape_trends(self, region: str, category: str = None) -> List[TrendData]:
        """
        Scrape trending topics from Trends24.in.
        
        Args:
            region: Region code (US, UK, CA, AU)
            category: Optional category filter (not used by Trends24)
            
        Returns:
            List of TrendData objects
        """
        self.logger.info("Starting Trends24 scraping", region=region, category=category)
        
        try:
            # Apply rate limiting
            await self.rate_limiter.acquire()
            
            # Map region to trends24 format
            country_slug = self.region_mapping.get(region, "united-states")
            
            # Scrape trends from the country page
            trends = await self._scrape_country_trends(country_slug)
            
            # Convert to TrendData objects
            trend_data_list = []
            for trend in trends:
                trend_data = self._convert_to_trend_data(trend, region, category)
                if self.validate_data(trend_data):
                    trend_data_list.append(trend_data)
            
            self.logger.info(
                "Trends24 scraping completed",
                region=region,
                trends_found=len(trend_data_list)
            )
            
            return trend_data_list
            
        except Exception as e:
            self.logger.error(
                "Trends24 scraping failed",
                region=region,
                error=str(e)
            )
            raise
    
    async def _scrape_country_trends(self, country_slug: str) -> List[Dict[str, Any]]:
        """Scrape trends from a specific country page."""
        
        url = f"{self.base_url}/{country_slug}"
        headers = {
            "User-Agent": self._get_random_user_agent(),
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.5",
            "Accept-Encoding": "gzip, deflate",
            "Cache-Control": "no-cache"
        }
        
        try:
            async with self.session.get(url, headers=headers) as response:
                
                if response.status == 429:
                    raise RateLimitError("Trends24 rate limit exceeded")
                
                response.raise_for_status()
                html_content = await response.text()
                
                # Parse HTML content
                trends = self._parse_html_content(html_content)
                return trends
                
        except aiohttp.ClientError as e:
            self.logger.error("HTTP request failed", error=str(e))
            raise ConnectionError(f"Failed to fetch trends: {e}")
    
    def _parse_html_content(self, html_content: str) -> List[Dict[str, Any]]:
        """Parse HTML content to extract trending topics."""
        
        trends = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Find trending topics - this selector may need adjustment based on site structure
            trend_elements = soup.find_all(['li', 'div'], class_=['trend-item', 'trending-item'])
            
            # If specific classes don't work, try finding by text patterns
            if not trend_elements:
                # Look for elements containing hashtags or trending keywords
                trend_elements = soup.find_all(text=lambda text: text and '#' in text)
            
            for element in trend_elements[:20]:  # Limit to top 20 trends
                if isinstance(element, str):
                    # Direct text element
                    keyword = element.strip()
                else:
                    # HTML element
                    keyword = element.get_text(strip=True)
                
                if keyword and len(keyword) > 1:
                    # Clean up the keyword
                    keyword = self._clean_keyword(keyword)
                    
                    if keyword:
                        trend = {
                            "keyword": keyword,
                            "rank": len(trends) + 1,
                            "search_volume": self._estimate_search_volume(len(trends) + 1),
                            "growth_rate": self._estimate_growth_rate(),
                            "source_url": self.base_url
                        }
                        trends.append(trend)
            
        except Exception as e:
            self.logger.error("Failed to parse HTML content", error=str(e))
        
        return trends
    
    def _clean_keyword(self, keyword: str) -> str:
        """Clean and normalize keyword from Trends24."""
        if not keyword:
            return ""
        
        # Remove common prefixes/suffixes
        keyword = keyword.strip()
        
        # Remove hashtag symbol if present
        if keyword.startswith('#'):
            keyword = keyword[1:]
        
        # Remove numbers at the beginning (ranking numbers)
        import re
        keyword = re.sub(r'^\d+\.?\s*', '', keyword)
        
        # Remove extra whitespace
        keyword = ' '.join(keyword.split())
        
        # Skip if too short or contains only numbers
        if len(keyword) < 2 or keyword.isdigit():
            return ""
        
        # Skip common non-trend words
        skip_words = ['trending', 'trends', 'now', 'today', 'hot']
        if keyword.lower() in skip_words:
            return ""
        
        return keyword
    
    def _convert_to_trend_data(
        self, 
        trend: Dict[str, Any], 
        region: str, 
        category: str
    ) -> TrendData:
        """Convert raw trend data to TrendData object."""
        
        return TrendData(
            keyword=self.normalize_keyword(trend["keyword"]),
            search_volume=trend.get("search_volume"),
            growth_rate=trend.get("growth_rate"),
            region=region,
            category=category or "Social Media",  # Trends24 is primarily Twitter trends
            source="trends24",
            timestamp=datetime.utcnow(),
            raw_data=trend,
            confidence_score=0.7  # Slightly lower confidence than Google Trends
        )
    
    def validate_data(self, data: TrendData) -> bool:
        """Validate Trends24 data."""
        if not data.keyword or len(data.keyword.strip()) == 0:
            return False
        
        if len(data.keyword) > 255:  # Database constraint
            return False
        
        # Skip very short keywords (likely noise)
        if len(data.keyword.strip()) < 2:
            return False
        
        # Skip keywords that are just numbers
        if data.keyword.strip().isdigit():
            return False
        
        if data.search_volume is not None and data.search_volume < 0:
            return False
        
        if data.growth_rate is not None and data.growth_rate < 0:
            return False
        
        return True
    
    async def test_connection(self) -> bool:
        """Test connection to Trends24."""
        try:
            headers = {"User-Agent": self._get_random_user_agent()}
            
            async with self.session.get(self.base_url, headers=headers) as response:
                return response.status == 200
                
        except Exception as e:
            self.logger.error("Trends24 connection test failed", error=str(e))
            return False
    
    def _get_random_user_agent(self) -> str:
        """Get a random user agent string."""
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0"
        ]
        return random.choice(user_agents)
    
    def _estimate_search_volume(self, rank: int) -> int:
        """Estimate search volume based on trend ranking."""
        # Higher ranked trends get higher estimated volume
        base_volume = 50000
        rank_multiplier = max(1, 21 - rank)  # Rank 1 gets 20x, rank 20 gets 1x
        return int(base_volume * rank_multiplier * random.uniform(0.5, 1.5))
    
    def _estimate_growth_rate(self) -> float:
        """Estimate growth rate for Twitter trends."""
        # Twitter trends tend to have high growth rates
        return random.uniform(1.0, 10.0)
    
    def get_rate_limit(self) -> Dict[str, int]:
        """Get rate limiting configuration."""
        return {
            "requests_per_minute": self.rate_limit_rpm,
            "requests_per_hour": self.rate_limit_rpm * 60,
            "burst_limit": 5
        }
    
    def get_supported_regions(self) -> List[str]:
        """Get list of supported regions."""
        return list(self.region_mapping.keys())
    
    def get_supported_categories(self) -> List[str]:
        """Get list of supported categories."""
        # Trends24 doesn't have specific categories, it's mainly social media trends
        return ["Social Media", "Entertainment", "Technology", "General"]
