{% extends "base.html" %}

{% block title %}{{ title }} - {{ site_name }}{% endblock %}

{% block meta_keywords %}{{ keyword }}, {{ category }}, trending, {{ keyword | slug }}{% endblock %}

{% block structured_data %}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "{{ title }}",
  "description": "{{ meta_description }}",
  "author": {
    "@type": "Person",
    "name": "{{ author | default('Trend Platform') }}"
  },
  "publisher": {
    "@type": "Organization",
    "name": "{{ site_name }}",
    "logo": {
      "@type": "ImageObject",
      "url": "{{ canonical_url | default('') }}/logo.png"
    }
  },
  "datePublished": "{{ publish_date.isoformat() }}",
  "dateModified": "{{ publish_date.isoformat() }}",
  {% if hero_image_url %}
  "image": "{{ hero_image_url }}",
  {% endif %}
  "mainEntityOfPage": {
    "@type": "WebPage",
    "@id": "{{ canonical_url | default('') }}"
  },
  "articleSection": "{{ category }}",
  "keywords": "{{ keyword }}, {{ category }}, trending"
}
</script>
{% endblock %}

{% block body_class %}article-page{% endblock %}

{% block content %}
<article class="article">
    <div class="container">
        <!-- Article Header -->
        <header class="article-header">
            <div class="article-meta">
                <span class="category-badge category-{{ category | lower | slug }}">{{ category }}</span>
                <time datetime="{{ publish_date.isoformat() }}" class="publish-date">
                    {{ publish_date.strftime('%B %d, %Y') }}
                </time>
                <span class="reading-time">{{ reading_time }} min read</span>
            </div>
            
            <h1 class="article-title">{{ title }}</h1>
            
            <div class="article-summary">
                <p>{{ meta_description }}</p>
            </div>
            
            {% if hero_image_url %}
            <div class="hero-image">
                <img src="{{ hero_image_url }}" alt="{{ title }}" loading="lazy">
            </div>
            {% endif %}
        </header>

        <!-- Article Content -->
        <div class="article-content">
            {{ content | safe }}
            
            {% if code_snippet %}
            <div class="code-snippet-section">
                <h3>Code Example</h3>
                <div class="code-snippet">
                    <div class="code-header">
                        <span class="language">{{ code_snippet.language }}</span>
                        <button class="copy-button" onclick="copyCode(this)">Copy</button>
                    </div>
                    <pre><code class="language-{{ code_snippet.language }}">{{ code_snippet.code }}</code></pre>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Article Footer -->
        <footer class="article-footer">
            <div class="article-stats">
                <span class="word-count">{{ word_count }} words</span>
                <span class="category">Category: {{ category }}</span>
                <span class="keyword">Trending: {{ keyword }}</span>
            </div>
            
            <div class="share-buttons">
                <h4>Share this article:</h4>
                <div class="share-links">
                    <a href="https://twitter.com/intent/tweet?text={{ title | urlencode }}&url={{ canonical_url | urlencode }}" 
                       target="_blank" rel="noopener" class="share-twitter">
                        Twitter
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ canonical_url | urlencode }}" 
                       target="_blank" rel="noopener" class="share-facebook">
                        Facebook
                    </a>
                    <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ canonical_url | urlencode }}" 
                       target="_blank" rel="noopener" class="share-linkedin">
                        LinkedIn
                    </a>
                    <a href="mailto:?subject={{ title | urlencode }}&body={{ canonical_url | urlencode }}" 
                       class="share-email">
                        Email
                    </a>
                </div>
            </div>
        </footer>
    </div>
</article>

<!-- Related Articles Section -->
{% if related_articles %}
<section class="related-articles">
    <div class="container">
        <h2>Related Trending Topics</h2>
        <div class="articles-grid">
            {% for article in related_articles %}
            <article class="article-card">
                <a href="{{ article.url }}" class="article-link">
                    {% if article.image %}
                    <div class="article-image">
                        <img src="{{ article.image }}" alt="{{ article.title }}" loading="lazy">
                    </div>
                    {% endif %}
                    <div class="article-info">
                        <span class="category-badge category-{{ article.category | lower | slug }}">
                            {{ article.category }}
                        </span>
                        <h3 class="article-title">{{ article.title }}</h3>
                        <p class="article-excerpt">{{ article.excerpt | truncate_words(20) }}</p>
                        <time class="article-date">{{ article.date.strftime('%B %d, %Y') }}</time>
                    </div>
                </a>
            </article>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="/assets/css/article.css">
<link rel="stylesheet" href="/assets/css/syntax-highlighting.css">
{% endblock %}

{% block extra_js %}
<script>
function copyCode(button) {
    const codeBlock = button.parentElement.nextElementSibling.querySelector('code');
    const text = codeBlock.textContent;
    
    navigator.clipboard.writeText(text).then(function() {
        button.textContent = 'Copied!';
        setTimeout(function() {
            button.textContent = 'Copy';
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy code: ', err);
    });
}

// Track article engagement
document.addEventListener('DOMContentLoaded', function() {
    // Track scroll depth
    let maxScroll = 0;
    window.addEventListener('scroll', function() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
        }
    });
    
    // Track time on page
    const startTime = Date.now();
    window.addEventListener('beforeunload', function() {
        const timeOnPage = Math.round((Date.now() - startTime) / 1000);
        
        // Send analytics data
        if (typeof gtag !== 'undefined') {
            gtag('event', 'article_engagement', {
                'article_title': '{{ title }}',
                'article_category': '{{ category }}',
                'keyword': '{{ keyword }}',
                'time_on_page': timeOnPage,
                'scroll_depth': maxScroll,
                'word_count': {{ word_count }}
            });
        }
    });
});
</script>
{% endblock %}
