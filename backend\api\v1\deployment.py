"""
Deployment API endpoints.

Provides REST API endpoints for deployment management, monitoring,
and control operations.
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from database.connection import get_database_pool
from database.models.deployment_model import DeploymentRepository
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository
from deployment.deployment_orchestrator import DeploymentOrchestrator
from deployment.domain_manager import DomainManager
from deployment.tasks import (
    deploy_trend_site_task,
    batch_deploy_trends_task,
    setup_custom_domain_task
)
from app.auth import get_current_user
from app.config import settings
import structlog

logger = structlog.get_logger()

router = APIRouter(prefix="/deployment", tags=["deployment"])


# Pydantic models for request/response
class DeploymentRequest(BaseModel):
    """Request model for deployment creation."""
    trend_id: str
    force_redeploy: bool = False
    custom_domain: Optional[str] = None


class BatchDeploymentRequest(BaseModel):
    """Request model for batch deployment."""
    trend_ids: List[str] = Field(..., min_items=1, max_items=20)
    max_concurrent: int = Field(default=3, ge=1, le=10)


class CustomDomainRequest(BaseModel):
    """Request model for custom domain setup."""
    custom_domain: str = Field(..., regex=r'^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\..*$')


class DeploymentResponse(BaseModel):
    """Response model for deployment operations."""
    success: bool
    deployment_id: Optional[str] = None
    message: str
    data: Optional[Dict[str, Any]] = None


async def get_deployment_repo():
    """Dependency to get deployment repository."""
    pool = await get_database_pool()
    return DeploymentRepository(pool)


async def get_trend_repo():
    """Dependency to get trend repository."""
    pool = await get_database_pool()
    return TrendRepository(pool)


async def get_content_repo():
    """Dependency to get content repository."""
    pool = await get_database_pool()
    return ContentRepository(pool)


@router.post("/deploy", response_model=DeploymentResponse)
async def deploy_trend_site(
    request: DeploymentRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user),
    trend_repo: TrendRepository = Depends(get_trend_repo),
    content_repo: ContentRepository = Depends(get_content_repo)
):
    """
    Deploy a trend site.
    
    Initiates deployment of a static site for the specified trend.
    """
    try:
        # Verify trend exists and user has permission
        trend = await trend_repo.get_by_id(request.trend_id)
        if not trend:
            raise HTTPException(status_code=404, detail="Trend not found")
        
        # Check if trend has content
        content = await content_repo.get_by_trend_id(request.trend_id)
        if not content:
            raise HTTPException(
                status_code=400, 
                detail="Trend must have generated content before deployment"
            )
        
        # Start deployment task
        task = deploy_trend_site_task.delay(
            trend_id=request.trend_id,
            force_redeploy=request.force_redeploy,
            custom_domain=request.custom_domain
        )
        
        logger.info(
            "Deployment task started",
            trend_id=request.trend_id,
            task_id=task.id,
            user_id=current_user.get("id")
        )
        
        return DeploymentResponse(
            success=True,
            message="Deployment started successfully",
            data={
                "task_id": task.id,
                "trend_id": request.trend_id,
                "trend_keyword": trend["keyword"]
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Deployment initiation failed", trend_id=request.trend_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Deployment failed: {str(e)}")


@router.post("/batch-deploy", response_model=DeploymentResponse)
async def batch_deploy_trends(
    request: BatchDeploymentRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user),
    trend_repo: TrendRepository = Depends(get_trend_repo)
):
    """
    Deploy multiple trend sites in batch.
    
    Initiates deployment of multiple trends concurrently.
    """
    try:
        # Verify all trends exist
        valid_trends = []
        for trend_id in request.trend_ids:
            trend = await trend_repo.get_by_id(trend_id)
            if trend:
                valid_trends.append(trend_id)
        
        if not valid_trends:
            raise HTTPException(status_code=400, detail="No valid trends found")
        
        # Start batch deployment task
        task = batch_deploy_trends_task.delay(
            trend_ids=valid_trends,
            max_concurrent=request.max_concurrent
        )
        
        logger.info(
            "Batch deployment task started",
            trend_count=len(valid_trends),
            task_id=task.id,
            user_id=current_user.get("id")
        )
        
        return DeploymentResponse(
            success=True,
            message=f"Batch deployment started for {len(valid_trends)} trends",
            data={
                "task_id": task.id,
                "trend_count": len(valid_trends),
                "valid_trends": valid_trends,
                "invalid_trends": list(set(request.trend_ids) - set(valid_trends))
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Batch deployment failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Batch deployment failed: {str(e)}")


@router.get("/status/{deployment_id}")
async def get_deployment_status(
    deployment_id: str,
    current_user: dict = Depends(get_current_user),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repo)
):
    """Get detailed deployment status."""
    try:
        deployment = await deployment_repo.get_deployment_with_trend(deployment_id)
        if not deployment:
            raise HTTPException(status_code=404, detail="Deployment not found")
        
        # Get additional status from orchestrator if needed
        config = {
            "coolify": {
                "api_url": settings.coolify_api_url,
                "api_token": settings.coolify_api_token
            }
        }
        
        orchestrator = DeploymentOrchestrator(
            deployment_repository=deployment_repo,
            trend_repository=None,
            content_repository=None,
            config=config
        )
        
        try:
            detailed_status = await orchestrator.get_deployment_status(deployment_id)
            return detailed_status
        finally:
            await orchestrator.close()
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get deployment status", deployment_id=deployment_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get status: {str(e)}")


@router.get("/list")
async def list_deployments(
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    trend_id: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repo)
):
    """List deployments with pagination and filtering."""
    try:
        filters = {}
        if status:
            filters["status"] = status
        if trend_id:
            filters["trend_id"] = trend_id
        
        result = await deployment_repo.list_with_pagination(
            page=page,
            page_size=page_size,
            filters=filters
        )
        
        return result
    
    except Exception as e:
        logger.error("Failed to list deployments", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to list deployments: {str(e)}")


@router.get("/metrics")
async def get_deployment_metrics(
    days: int = Query(7, ge=1, le=90),
    current_user: dict = Depends(get_current_user),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repo)
):
    """Get deployment metrics and statistics."""
    try:
        stats = await deployment_repo.get_deployment_stats()
        recent_deployments = await deployment_repo.get_recent_deployments(days)
        
        return {
            "stats": stats,
            "recent_deployments_count": len(recent_deployments),
            "period_days": days,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    except Exception as e:
        logger.error("Failed to get deployment metrics", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get metrics: {str(e)}")


@router.post("/{deployment_id}/setup-domain", response_model=DeploymentResponse)
async def setup_custom_domain(
    deployment_id: str,
    request: CustomDomainRequest,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_current_user),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repo)
):
    """Setup custom domain for a deployment."""
    try:
        # Verify deployment exists
        deployment = await deployment_repo.get_by_id(deployment_id)
        if not deployment:
            raise HTTPException(status_code=404, detail="Deployment not found")
        
        # Start domain setup task
        task = setup_custom_domain_task.delay(
            deployment_id=deployment_id,
            custom_domain=request.custom_domain
        )
        
        logger.info(
            "Custom domain setup started",
            deployment_id=deployment_id,
            domain=request.custom_domain,
            task_id=task.id
        )
        
        return DeploymentResponse(
            success=True,
            message="Custom domain setup started",
            data={
                "task_id": task.id,
                "deployment_id": deployment_id,
                "custom_domain": request.custom_domain
            }
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Custom domain setup failed", deployment_id=deployment_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Domain setup failed: {str(e)}")


@router.delete("/{deployment_id}")
async def cancel_deployment(
    deployment_id: str,
    current_user: dict = Depends(get_current_user),
    deployment_repo: DeploymentRepository = Depends(get_deployment_repo)
):
    """Cancel a running deployment."""
    try:
        deployment = await deployment_repo.get_by_id(deployment_id)
        if not deployment:
            raise HTTPException(status_code=404, detail="Deployment not found")
        
        if deployment["status"] not in ["pending", "building"]:
            raise HTTPException(
                status_code=400, 
                detail=f"Cannot cancel deployment with status: {deployment['status']}"
            )
        
        # Initialize deployment tracker to cancel
        from deployment.coolify_client import CoolifyClient
        from deployment.deployment_tracker import DeploymentTracker
        
        coolify_client = CoolifyClient(
            api_url=settings.coolify_api_url,
            api_token=settings.coolify_api_token
        )
        
        tracker = DeploymentTracker(
            deployment_repository=deployment_repo,
            coolify_client=coolify_client
        )
        
        try:
            success = await tracker.cancel_deployment(deployment_id)
            
            if success:
                return {"success": True, "message": "Deployment cancelled successfully"}
            else:
                raise HTTPException(status_code=500, detail="Failed to cancel deployment")
        
        finally:
            await coolify_client.close()
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to cancel deployment", deployment_id=deployment_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to cancel: {str(e)}")


@router.get("/domains/status/{domain}")
async def get_domain_status(
    domain: str,
    current_user: dict = Depends(get_current_user)
):
    """Get DNS configuration status for a domain."""
    try:
        domain_manager = DomainManager({
            "cloudflare_api_token": settings.cloudflare_api_token,
            "zone_id": settings.cloudflare_zone_id,
            "base_domain": settings.base_domain
        })
        
        try:
            status = await domain_manager.get_domain_status(domain)
            return status
        finally:
            await domain_manager.close()
    
    except Exception as e:
        logger.error("Failed to get domain status", domain=domain, error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to get domain status: {str(e)}")


@router.get("/domains/list")
async def list_managed_domains(
    current_user: dict = Depends(get_current_user)
):
    """List all managed domains."""
    try:
        domain_manager = DomainManager({
            "cloudflare_api_token": settings.cloudflare_api_token,
            "zone_id": settings.cloudflare_zone_id,
            "base_domain": settings.base_domain
        })
        
        try:
            domains = await domain_manager.list_managed_domains()
            return {"domains": domains}
        finally:
            await domain_manager.close()
    
    except Exception as e:
        logger.error("Failed to list managed domains", error=str(e))
        raise HTTPException(status_code=500, detail=f"Failed to list domains: {str(e)}")


@router.get("/health")
async def deployment_health_check():
    """Health check for deployment services."""
    try:
        health_status = {
            "deployment_api": "healthy",
            "coolify_connection": "unknown",
            "cloudflare_connection": "unknown",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Test Coolify connection
        try:
            from deployment.coolify_client import CoolifyClient
            coolify_client = CoolifyClient(
                api_url=settings.coolify_api_url,
                api_token=settings.coolify_api_token
            )
            
            coolify_healthy = await coolify_client.test_connection()
            health_status["coolify_connection"] = "healthy" if coolify_healthy else "unhealthy"
            
            await coolify_client.close()
        except Exception as e:
            health_status["coolify_connection"] = f"error: {str(e)}"
        
        # Test Cloudflare connection
        try:
            domain_manager = DomainManager({
                "cloudflare_api_token": settings.cloudflare_api_token,
                "zone_id": settings.cloudflare_zone_id
            })
            
            if domain_manager.cloudflare_client:
                cf_healthy = await domain_manager.cloudflare_client.test_connection()
                health_status["cloudflare_connection"] = "healthy" if cf_healthy else "unhealthy"
                await domain_manager.close()
            else:
                health_status["cloudflare_connection"] = "not_configured"
        except Exception as e:
            health_status["cloudflare_connection"] = f"error: {str(e)}"
        
        return health_status
    
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")
