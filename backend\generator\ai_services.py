"""
AI services for content generation.

Provides interfaces to various AI services for text generation,
image creation, and content enhancement.
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from abc import ABC, abstractmethod
import json
import structlog

logger = structlog.get_logger()


@dataclass
class GenerationRequest:
    """Request for AI content generation."""
    prompt: str
    max_tokens: int = 2000
    temperature: float = 0.7
    model: str = "gpt-3.5-turbo"
    system_prompt: Optional[str] = None
    context: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}


@dataclass
class GenerationResponse:
    """Response from AI content generation."""
    content: str
    model_used: str
    tokens_used: int
    finish_reason: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class BaseAIService(ABC):
    """Base class for AI service implementations."""
    
    def __init__(self, name: str, config: Dict[str, Any] = None):
        self.name = name
        self.config = config or {}
        self.logger = structlog.get_logger().bind(service=name)
    
    @abstractmethod
    async def generate_text(self, request: GenerationRequest) -> GenerationResponse:
        """Generate text content."""
        pass
    
    @abstractmethod
    async def generate_image(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate image content."""
        pass
    
    async def test_connection(self) -> bool:
        """Test if the service is accessible."""
        try:
            test_request = GenerationRequest(
                prompt="Test connection",
                max_tokens=10
            )
            response = await self.generate_text(test_request)
            return bool(response.content)
        except Exception as e:
            self.logger.error("Connection test failed", error=str(e))
            return False


class OpenAIService(BaseAIService):
    """OpenAI API service implementation."""
    
    def __init__(self, api_key: str, base_url: str = "https://api.openai.com/v1", **kwargs):
        super().__init__("openai", kwargs)
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session = None
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self.session is None:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=120)
            self.session = aiohttp.ClientSession(headers=headers, timeout=timeout)
        return self.session
    
    async def generate_text(self, request: GenerationRequest) -> GenerationResponse:
        """Generate text using OpenAI API."""
        session = await self._get_session()
        
        # Prepare messages
        messages = []
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})
        messages.append({"role": "user", "content": request.prompt})
        
        # Prepare request payload
        payload = {
            "model": request.model,
            "messages": messages,
            "max_tokens": request.max_tokens,
            "temperature": request.temperature,
            "stream": False
        }
        
        try:
            async with session.post(f"{self.base_url}/chat/completions", json=payload) as response:
                response.raise_for_status()
                data = await response.json()
                
                choice = data["choices"][0]
                usage = data.get("usage", {})
                
                return GenerationResponse(
                    content=choice["message"]["content"],
                    model_used=data["model"],
                    tokens_used=usage.get("total_tokens", 0),
                    finish_reason=choice.get("finish_reason", "unknown"),
                    metadata={
                        "prompt_tokens": usage.get("prompt_tokens", 0),
                        "completion_tokens": usage.get("completion_tokens", 0)
                    }
                )
        
        except aiohttp.ClientError as e:
            self.logger.error("OpenAI API request failed", error=str(e))
            raise
        except Exception as e:
            self.logger.error("Text generation failed", error=str(e))
            raise
    
    async def generate_image(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate image using DALL-E."""
        session = await self._get_session()
        
        payload = {
            "prompt": prompt,
            "n": kwargs.get("n", 1),
            "size": kwargs.get("size", "1024x1024"),
            "quality": kwargs.get("quality", "standard"),
            "response_format": kwargs.get("response_format", "url")
        }
        
        # Use DALL-E 3 by default
        model = kwargs.get("model", "dall-e-3")
        payload["model"] = model
        
        try:
            async with session.post(f"{self.base_url}/images/generations", json=payload) as response:
                response.raise_for_status()
                data = await response.json()
                
                return {
                    "images": data["data"],
                    "model_used": model,
                    "prompt": prompt,
                    "metadata": {
                        "size": payload["size"],
                        "quality": payload["quality"]
                    }
                }
        
        except aiohttp.ClientError as e:
            self.logger.error("OpenAI image generation failed", error=str(e))
            raise
        except Exception as e:
            self.logger.error("Image generation failed", error=str(e))
            raise
    
    async def close(self):
        """Close the HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None


class AIContentGenerator:
    """Main content generator that orchestrates AI services."""
    
    def __init__(self, ai_service: BaseAIService, config: Dict[str, Any] = None):
        self.ai_service = ai_service
        self.config = config or {}
        self.logger = structlog.get_logger().bind(component="ai_content_generator")
        
        # Content generation templates
        self.templates = {
            "article": self._get_article_template(),
            "summary": self._get_summary_template(),
            "meta_description": self._get_meta_description_template(),
            "code_snippet": self._get_code_snippet_template()
        }
    
    async def generate_article(
        self,
        keyword: str,
        category: str,
        region: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Generate a complete article for a trend."""
        self.logger.info("Generating article", keyword=keyword, category=category)
        
        context = context or {}
        
        # Generate main content
        article_prompt = self.templates["article"].format(
            keyword=keyword,
            category=category,
            region=region,
            **context
        )
        
        article_request = GenerationRequest(
            prompt=article_prompt,
            max_tokens=self.config.get("max_article_length", 2000),
            temperature=0.7,
            system_prompt="You are a skilled content writer creating engaging, informative articles about trending topics."
        )
        
        article_response = await self.ai_service.generate_text(article_request)
        
        # Generate meta description
        meta_prompt = self.templates["meta_description"].format(
            keyword=keyword,
            article_content=article_response.content[:500]  # First 500 chars
        )
        
        meta_request = GenerationRequest(
            prompt=meta_prompt,
            max_tokens=160,
            temperature=0.5
        )
        
        meta_response = await self.ai_service.generate_text(meta_request)
        
        # Generate code snippet if it's a tech-related trend
        code_snippet = None
        if category.lower() in ["technology", "programming", "software"]:
            code_snippet = await self._generate_code_snippet(keyword, article_response.content)
        
        return {
            "title": self._extract_title(article_response.content),
            "content": self._clean_content(article_response.content),
            "meta_description": meta_response.content.strip(),
            "code_snippet": code_snippet,
            "word_count": len(article_response.content.split()),
            "generation_metadata": {
                "tokens_used": article_response.tokens_used + meta_response.tokens_used,
                "model_used": article_response.model_used,
                "generation_time": context.get("generation_time")
            }
        }
    
    async def generate_hero_image(
        self,
        keyword: str,
        category: str,
        article_content: str = None
    ) -> Dict[str, Any]:
        """Generate a hero image for the article."""
        self.logger.info("Generating hero image", keyword=keyword, category=category)
        
        # Create image prompt based on keyword and category
        image_prompt = self._create_image_prompt(keyword, category, article_content)
        
        try:
            image_result = await self.ai_service.generate_image(
                prompt=image_prompt,
                size="1024x1024",
                quality="standard"
            )
            
            return {
                "image_url": image_result["images"][0]["url"],
                "prompt_used": image_prompt,
                "metadata": image_result["metadata"]
            }
        
        except Exception as e:
            self.logger.error("Hero image generation failed", error=str(e))
            return None
    
    async def _generate_code_snippet(self, keyword: str, article_content: str) -> Optional[Dict[str, str]]:
        """Generate a relevant code snippet."""
        code_prompt = self.templates["code_snippet"].format(
            keyword=keyword,
            article_content=article_content[:1000]
        )
        
        code_request = GenerationRequest(
            prompt=code_prompt,
            max_tokens=500,
            temperature=0.3,
            system_prompt="You are a programming expert. Generate clean, well-commented code examples."
        )
        
        try:
            code_response = await self.ai_service.generate_text(code_request)
            
            # Extract language and code
            content = code_response.content.strip()
            if "```" in content:
                parts = content.split("```")
                if len(parts) >= 3:
                    language_line = parts[1].split('\n')[0].strip()
                    code = '\n'.join(parts[1].split('\n')[1:]).strip()
                    
                    return {
                        "language": language_line or "text",
                        "code": code
                    }
            
            return {
                "language": "text",
                "code": content
            }
        
        except Exception as e:
            self.logger.warning("Code snippet generation failed", error=str(e))
            return None
    
    def _extract_title(self, content: str) -> str:
        """Extract title from generated content."""
        lines = content.strip().split('\n')
        
        # Look for markdown headers
        for line in lines:
            line = line.strip()
            if line.startswith('# '):
                return line[2:].strip()
            elif line.startswith('## '):
                return line[3:].strip()
        
        # Fallback: use first line
        if lines:
            return lines[0].strip()
        
        return "Trending Topic"
    
    def _clean_content(self, content: str) -> str:
        """Clean and format the generated content."""
        # Remove title if it's at the beginning
        lines = content.strip().split('\n')
        if lines and (lines[0].startswith('# ') or lines[0].startswith('## ')):
            lines = lines[1:]
        
        # Join and clean up
        cleaned = '\n'.join(lines).strip()
        
        # Remove excessive newlines
        while '\n\n\n' in cleaned:
            cleaned = cleaned.replace('\n\n\n', '\n\n')
        
        return cleaned
    
    def _create_image_prompt(self, keyword: str, category: str, article_content: str = None) -> str:
        """Create an image generation prompt."""
        base_prompt = f"A professional, modern illustration representing '{keyword}'"
        
        # Add category-specific styling
        category_styles = {
            "technology": "with sleek, futuristic design elements, digital aesthetics",
            "health": "with clean, medical imagery, calming colors",
            "business": "with corporate, professional styling, charts or graphs",
            "entertainment": "with vibrant, dynamic visuals, engaging composition",
            "science": "with scientific imagery, laboratory or research themes"
        }
        
        style = category_styles.get(category.lower(), "with clean, professional design")
        
        return f"{base_prompt} {style}. High quality, suitable for web use, no text overlay."
    
    def _get_article_template(self) -> str:
        """Get article generation template."""
        return """Write a comprehensive, engaging article about the trending topic "{keyword}" in the {category} category for the {region} region.

The article should:
- Be informative and well-researched
- Include relevant examples and context
- Be 800-1500 words long
- Have a clear structure with subheadings
- Be engaging for a general audience
- Include current trends and developments
- Be optimized for web reading

Topic: {keyword}
Category: {category}
Region: {region}

Write the article in markdown format with appropriate headings."""
    
    def _get_summary_template(self) -> str:
        """Get summary generation template."""
        return """Create a concise, engaging summary of the following article about "{keyword}":

{article_content}

The summary should be 2-3 sentences and capture the main points."""
    
    def _get_meta_description_template(self) -> str:
        """Get meta description template."""
        return """Create an SEO-optimized meta description (150-160 characters) for an article about "{keyword}".

Article preview: {article_content}

The meta description should be compelling and include the main keyword."""
    
    def _get_code_snippet_template(self) -> str:
        """Get code snippet generation template."""
        return """Based on the trending topic "{keyword}" and this article content:

{article_content}

Generate a relevant, practical code example that demonstrates or relates to this topic. Include:
- Clean, well-commented code
- Appropriate programming language
- Practical example that readers can use

Format as a code block with language specification."""
    
    async def close(self):
        """Close AI service connections."""
        if hasattr(self.ai_service, 'close'):
            await self.ai_service.close()
