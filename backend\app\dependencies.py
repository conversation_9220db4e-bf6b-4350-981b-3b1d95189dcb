"""
Dependency injection for the Trend Platform API.

Provides common dependencies like database connections, authentication,
and service instances that can be injected into API endpoints.
"""

from typing import Optional, Dict, Any
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JWTError, jwt
import asyncpg
import structlog

from .config import settings
from database.connection import get_database_pool
from database.models.trend_model import TrendRepository
from database.models.content_model import ContentRepository
from database.models.deployment_model import DeploymentRepository
from database.models.user_model import UserProfileRepository

logger = structlog.get_logger()

# Security scheme for JWT authentication
security = HTTPBearer()


async def get_db_pool() -> asyncpg.Pool:
    """Get database connection pool dependency."""
    return await get_database_pool()


async def get_trend_repository(pool: asyncpg.Pool = Depends(get_db_pool)) -> TrendRepository:
    """Get trend repository dependency."""
    return TrendRepository(pool)


async def get_content_repository(pool: asyncpg.Pool = Depends(get_db_pool)) -> ContentRepository:
    """Get content repository dependency."""
    return ContentRepository(pool)


async def get_deployment_repository(pool: asyncpg.Pool = Depends(get_db_pool)) -> DeploymentRepository:
    """Get deployment repository dependency."""
    return DeploymentRepository(pool)


async def get_user_repository(pool: asyncpg.Pool = Depends(get_db_pool)) -> UserProfileRepository:
    """Get user profile repository dependency."""
    return UserProfileRepository(pool)


def verify_token(token: str) -> Dict[str, Any]:
    """Verify JWT token and return payload."""
    try:
        payload = jwt.decode(
            token,
            settings.jwt_secret_key,
            algorithms=[settings.jwt_algorithm]
        )
        return payload
    except JWTError as e:
        logger.warning("JWT verification failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    user_repo: UserProfileRepository = Depends(get_user_repository)
) -> Dict[str, Any]:
    """Get current authenticated user."""
    token = credentials.credentials
    payload = verify_token(token)
    
    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Get user profile from database
    user_profile = await user_repo.get_by_id(user_id)
    if user_profile is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user_profile


async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """Get current active user (can be extended with active status check)."""
    # Add any additional checks for user status here
    return current_user


def require_role(required_role: str):
    """Dependency factory for role-based access control."""
    async def role_checker(
        current_user: Dict[str, Any] = Depends(get_current_active_user)
    ) -> Dict[str, Any]:
        user_role = current_user.get("role", "viewer")
        
        # Define role hierarchy
        role_hierarchy = {
            "viewer": 0,
            "editor": 1,
            "admin": 2
        }
        
        required_level = role_hierarchy.get(required_role, 0)
        user_level = role_hierarchy.get(user_role, 0)
        
        if user_level < required_level:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required role: {required_role}"
            )
        
        return current_user
    
    return role_checker


def require_admin():
    """Require admin role dependency."""
    return require_role("admin")


def require_editor():
    """Require editor role dependency."""
    return require_role("editor")


async def get_optional_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    user_repo: UserProfileRepository = Depends(get_user_repository)
) -> Optional[Dict[str, Any]]:
    """Get current user if authenticated, otherwise return None."""
    if credentials is None:
        return None
    
    try:
        token = credentials.credentials
        payload = verify_token(token)
        user_id = payload.get("sub")
        
        if user_id:
            user_profile = await user_repo.get_by_id(user_id)
            return user_profile
    except HTTPException:
        # Invalid token, but we don't raise error for optional auth
        pass
    
    return None


class PaginationParams:
    """Pagination parameters for list endpoints."""
    
    def __init__(self, page: int = 1, page_size: int = 20):
        if page < 1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Page must be greater than 0"
            )
        
        if page_size < 1 or page_size > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Page size must be between 1 and 100"
            )
        
        self.page = page
        self.page_size = page_size


def get_pagination_params(page: int = 1, page_size: int = 20) -> PaginationParams:
    """Get pagination parameters dependency."""
    return PaginationParams(page, page_size)


# Global service instances (will be initialized during startup)
_services: Dict[str, Any] = {}


async def setup_dependencies():
    """Initialize global service instances."""
    logger.info("Setting up application dependencies")
    
    # Initialize any global services here
    # For example: AI clients, external API clients, etc.
    
    logger.info("Dependencies setup completed")


def get_service(service_name: str) -> Any:
    """Get a global service instance."""
    service = _services.get(service_name)
    if service is None:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Service {service_name} not available"
        )
    return service


# Rate limiting dependency (placeholder for future implementation)
async def rate_limit_check():
    """Check rate limits for the current request."""
    # TODO: Implement rate limiting logic
    # This could use Redis to track request counts per IP/user
    pass
