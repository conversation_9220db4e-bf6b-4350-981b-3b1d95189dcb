"""
Configuration management for the Trend Platform backend.

Handles environment variables, settings validation, and configuration
for different environments (development, staging, production).
"""

from typing import List, Optional
from pydantic import BaseSettings, Field, validator
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Application settings
    app_name: str = "Trend Platform API"
    app_version: str = "0.1.0"
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    api_v1_prefix: str = Field(default="/api/v1", env="API_V1_PREFIX")
    
    # Server settings
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    workers: int = Field(default=1, env="WORKERS")
    
    # Database settings
    database_url: str = Field(..., env="DATABASE_URL")
    database_min_connections: int = Field(default=5, env="DATABASE_MIN_CONNECTIONS")
    database_max_connections: int = Field(default=20, env="DATABASE_MAX_CONNECTIONS")
    
    # Supabase settings
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_anon_key: str = Field(..., env="SUPABASE_ANON_KEY")
    supabase_service_role_key: str = Field(..., env="SUPABASE_SERVICE_ROLE_KEY")
    
    # Redis settings
    redis_url: str = Field(default="redis://localhost:6379/0", env="REDIS_URL")
    
    # Celery settings
    celery_broker_url: str = Field(..., env="CELERY_BROKER_URL")
    celery_result_backend: str = Field(..., env="CELERY_RESULT_BACKEND")
    
    # Authentication settings
    jwt_secret_key: str = Field(..., env="JWT_SECRET_KEY")
    jwt_algorithm: str = Field(default="HS256", env="JWT_ALGORITHM")
    jwt_access_token_expire_minutes: int = Field(default=30, env="JWT_ACCESS_TOKEN_EXPIRE_MINUTES")
    
    # AI Services settings
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    openai_base_url: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    
    # Scraping settings
    google_trends_api_key: Optional[str] = Field(None, env="GOOGLE_TRENDS_API_KEY")
    proxy_list: List[str] = Field(default_factory=list, env="PROXY_LIST")
    
    # Git repository settings
    content_repo_url: Optional[str] = Field(None, env="CONTENT_REPO_URL")
    github_token: Optional[str] = Field(None, env="GITHUB_TOKEN")
    github_webhook_secret: Optional[str] = Field(None, env="GITHUB_WEBHOOK_SECRET")
    
    # Coolify settings
    coolify_api_url: Optional[str] = Field(None, env="COOLIFY_API_URL")
    coolify_api_token: Optional[str] = Field(None, env="COOLIFY_API_TOKEN")
    coolify_team_id: Optional[str] = Field(None, env="COOLIFY_TEAM_ID")
    coolify_app_uuid: Optional[str] = Field(None, env="COOLIFY_APP_UUID")
    
    # Cloudflare settings
    cloudflare_api_token: Optional[str] = Field(None, env="CLOUDFLARE_API_TOKEN")
    cloudflare_zone_id: Optional[str] = Field(None, env="CLOUDFLARE_ZONE_ID")
    cloudflare_account_id: Optional[str] = Field(None, env="CLOUDFLARE_ACCOUNT_ID")

    # Domain and DNS settings
    base_domain: str = Field(default="trends.example.com", env="BASE_DOMAIN")
    git_user: str = Field(default="Trend Platform", env="GIT_USER")
    git_email: str = Field(default="<EMAIL>", env="GIT_EMAIL")
    
    # Content generation settings
    max_article_length: int = Field(default=2000, env="MAX_ARTICLE_LENGTH")
    min_article_length: int = Field(default=500, env="MIN_ARTICLE_LENGTH")
    content_expiry_days: int = Field(default=14, env="CONTENT_EXPIRY_DAYS")
    image_optimization_enabled: bool = Field(default=True, env="IMAGE_OPTIMIZATION_ENABLED")
    webp_conversion_enabled: bool = Field(default=True, env="WEBP_CONVERSION_ENABLED")
    
    # Rate limiting settings
    rate_limit_requests_per_minute: int = Field(default=60, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    scraper_rate_limit_rpm: int = Field(default=10, env="SCRAPER_RATE_LIMIT_RPM")
    
    # Security settings
    vault_url: Optional[str] = Field(None, env="VAULT_URL")
    vault_token: Optional[str] = Field(None, env="VAULT_TOKEN")
    
    # CORS settings
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8000"],
        env="CORS_ORIGINS"
    )
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        env="CORS_ALLOW_METHODS"
    )
    cors_allow_headers: List[str] = Field(
        default=["*"],
        env="CORS_ALLOW_HEADERS"
    )
    
    @validator("proxy_list", pre=True)
    def parse_proxy_list(cls, v):
        """Parse comma-separated proxy list."""
        if isinstance(v, str):
            return [proxy.strip() for proxy in v.split(",") if proxy.strip()]
        return v
    
    @validator("cors_origins", pre=True)
    def parse_cors_origins(cls, v):
        """Parse comma-separated CORS origins."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",") if origin.strip()]
        return v
    
    @validator("cors_allow_methods", pre=True)
    def parse_cors_methods(cls, v):
        """Parse comma-separated CORS methods."""
        if isinstance(v, str):
            return [method.strip() for method in v.split(",") if method.strip()]
        return v
    
    @validator("cors_allow_headers", pre=True)
    def parse_cors_headers(cls, v):
        """Parse comma-separated CORS headers."""
        if isinstance(v, str):
            return [header.strip() for header in v.split(",") if header.strip()]
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"Log level must be one of: {valid_levels}")
        return v.upper()
    
    @validator("environment")
    def validate_environment(cls, v):
        """Validate environment."""
        valid_envs = ["development", "staging", "production"]
        if v.lower() not in valid_envs:
            raise ValueError(f"Environment must be one of: {valid_envs}")
        return v.lower()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings."""
    return settings


# Configuration for different modules
SCRAPER_CONFIG = {
    'regions': ['US', 'UK', 'CA', 'AU'],
    'categories': ['Technology', 'Health', 'Entertainment', 'Business', 'Science'],
    'proxy_rotation': {
        'enabled': bool(settings.proxy_list),
        'proxy_list': settings.proxy_list,
        'rotation_interval': 300  # 5 minutes
    },
    'rate_limiting': {
        'google_trends': {'requests_per_minute': settings.scraper_rate_limit_rpm},
        'trends24': {'requests_per_minute': 30}
    },
    'scoring': {
        'search_volume_weight': 0.6,
        'growth_rate_weight': 0.4,
        'minimum_score': 50
    }
}

GENERATOR_CONFIG = {
    'ai_services': {
        'text_generation': {
            'provider': 'openai',
            'api_key': settings.openai_api_key,
            'base_url': settings.openai_base_url,
            'model': 'gpt-3.5-turbo',
            'max_tokens': settings.max_article_length,
            'temperature': 0.7
        },
        'image_generation': {
            'provider': 'openai',
            'api_key': settings.openai_api_key,
            'model': 'dall-e-3',
            'size': '1024x1024',
            'quality': 'standard'
        }
    },
    'content': {
        'max_article_length': settings.max_article_length,
        'min_article_length': settings.min_article_length,
        'include_code_snippets': True,
        'moderation_enabled': True,
        'expiry_days': settings.content_expiry_days
    },
    'git': {
        'repository_url': settings.content_repo_url,
        'branch': 'main',
        'commit_message_template': 'Add content for trend: {keyword}'
    },
    'assets': {
        'image_optimization': settings.image_optimization_enabled,
        'webp_conversion': settings.webp_conversion_enabled,
        'max_image_width': 1200,
        'image_quality': 85
    }
}

DEPLOYMENT_CONFIG = {
    'coolify': {
        'api_url': settings.coolify_api_url,
        'api_token': settings.coolify_api_token,
        'team_id': settings.coolify_team_id,
        'app_uuid': settings.coolify_app_uuid,
        'build_pack': 'static',
        'timeout': 1800
    },
    'dns': {
        'cloudflare_api_token': settings.cloudflare_api_token,
        'zone_id': settings.cloudflare_zone_id,
        'base_domain': settings.base_domain,
        'enable_redirects': True,
        'proxied': True
    },
    'git': {
        'git_user': settings.git_user,
        'git_email': settings.git_email,
        'default_branch': 'main'
    },
    'tracking': {
        'poll_interval': 30,
        'max_deployment_time': 1800,
        'retry_failed_deployments': True,
        'max_retries': 3
    }
}
