"""
Deduplication utilities for trend data.

Provides fuzzy matching and deduplication capabilities to identify
and handle duplicate or similar trends from multiple sources.
"""

import re
from typing import List, Dict, Tuple, Set, Optional
from dataclasses import dataclass
from difflib import SequenceMatcher
from fuzzywuzzy import fuzz, process
import structlog

from .sources.base_scraper import TrendData

logger = structlog.get_logger()


@dataclass
class DuplicateMatch:
    """Information about a duplicate match."""
    original_trend: TrendData
    duplicate_trend: TrendData
    similarity_score: float
    match_type: str
    confidence: float


class FuzzyMatcher:
    """Fuzzy matching engine for trend deduplication."""
    
    def __init__(
        self,
        similarity_threshold: float = 0.85,
        exact_match_threshold: float = 0.95,
        partial_match_threshold: float = 0.80,
        token_sort_threshold: float = 0.85
    ):
        """
        Initialize fuzzy matcher.
        
        Args:
            similarity_threshold: Overall similarity threshold for duplicates
            exact_match_threshold: Threshold for exact string matching
            partial_match_threshold: Threshold for partial string matching
            token_sort_threshold: Threshold for token-sorted matching
        """
        self.similarity_threshold = similarity_threshold
        self.exact_match_threshold = exact_match_threshold
        self.partial_match_threshold = partial_match_threshold
        self.token_sort_threshold = token_sort_threshold
        
        self.logger = structlog.get_logger().bind(component="fuzzy_matcher")
    
    def find_duplicates(self, trends: List[TrendData]) -> List[DuplicateMatch]:
        """
        Find duplicate trends in a list.
        
        Args:
            trends: List of trends to check for duplicates
            
        Returns:
            List of duplicate matches found
        """
        self.logger.info("Starting duplicate detection", trend_count=len(trends))
        
        duplicates = []
        processed_indices = set()
        
        for i, trend1 in enumerate(trends):
            if i in processed_indices:
                continue
            
            for j, trend2 in enumerate(trends[i + 1:], i + 1):
                if j in processed_indices:
                    continue
                
                match = self._compare_trends(trend1, trend2)
                if match and match.similarity_score >= self.similarity_threshold:
                    duplicates.append(match)
                    processed_indices.add(j)  # Mark as processed to avoid duplicate reporting
        
        self.logger.info(
            "Duplicate detection completed",
            total_trends=len(trends),
            duplicates_found=len(duplicates)
        )
        
        return duplicates
    
    def _compare_trends(self, trend1: TrendData, trend2: TrendData) -> Optional[DuplicateMatch]:
        """
        Compare two trends for similarity.
        
        Args:
            trend1: First trend to compare
            trend2: Second trend to compare
            
        Returns:
            DuplicateMatch if trends are similar, None otherwise
        """
        # Normalize keywords for comparison
        keyword1 = self._normalize_keyword(trend1.keyword)
        keyword2 = self._normalize_keyword(trend2.keyword)
        
        # Calculate different similarity scores
        exact_score = fuzz.ratio(keyword1, keyword2) / 100.0
        partial_score = fuzz.partial_ratio(keyword1, keyword2) / 100.0
        token_sort_score = fuzz.token_sort_ratio(keyword1, keyword2) / 100.0
        token_set_score = fuzz.token_set_ratio(keyword1, keyword2) / 100.0
        
        # Determine match type and overall score
        match_type, overall_score, confidence = self._calculate_overall_similarity(
            exact_score, partial_score, token_sort_score, token_set_score
        )
        
        # Additional context-based scoring
        context_bonus = self._calculate_context_similarity(trend1, trend2)
        overall_score = min(1.0, overall_score + context_bonus)
        
        if overall_score >= self.similarity_threshold:
            return DuplicateMatch(
                original_trend=trend1,
                duplicate_trend=trend2,
                similarity_score=overall_score,
                match_type=match_type,
                confidence=confidence
            )
        
        return None
    
    def _normalize_keyword(self, keyword: str) -> str:
        """Normalize keyword for comparison."""
        if not keyword:
            return ""
        
        # Convert to lowercase
        normalized = keyword.lower()
        
        # Remove extra whitespace
        normalized = re.sub(r'\s+', ' ', normalized.strip())
        
        # Remove common punctuation
        normalized = re.sub(r'[^\w\s]', '', normalized)
        
        # Remove common stop words that don't affect meaning
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        words = normalized.split()
        words = [word for word in words if word not in stop_words]
        
        return ' '.join(words)
    
    def _calculate_overall_similarity(
        self, 
        exact_score: float, 
        partial_score: float, 
        token_sort_score: float, 
        token_set_score: float
    ) -> Tuple[str, float, float]:
        """
        Calculate overall similarity score and determine match type.
        
        Returns:
            Tuple of (match_type, overall_score, confidence)
        """
        # Determine match type based on highest score
        scores = {
            "exact": exact_score,
            "partial": partial_score,
            "token_sort": token_sort_score,
            "token_set": token_set_score
        }
        
        match_type = max(scores, key=scores.get)
        max_score = scores[match_type]
        
        # Calculate weighted overall score
        # Give more weight to exact and token_set matches
        weights = {
            "exact": 0.4,
            "partial": 0.2,
            "token_sort": 0.2,
            "token_set": 0.2
        }
        
        overall_score = sum(scores[score_type] * weight for score_type, weight in weights.items())
        
        # Calculate confidence based on score consistency
        score_variance = sum((score - overall_score) ** 2 for score in scores.values()) / len(scores)
        confidence = max(0.0, 1.0 - score_variance)
        
        return match_type, overall_score, confidence
    
    def _calculate_context_similarity(self, trend1: TrendData, trend2: TrendData) -> float:
        """Calculate similarity bonus based on context (region, category, source)."""
        bonus = 0.0
        
        # Same region bonus
        if trend1.region == trend2.region:
            bonus += 0.05
        
        # Same category bonus
        if trend1.category == trend2.category:
            bonus += 0.05
        
        # Same source penalty (likely to be duplicates if from same source)
        if trend1.source == trend2.source:
            bonus += 0.1
        
        # Similar search volume bonus
        if trend1.search_volume and trend2.search_volume:
            volume_ratio = min(trend1.search_volume, trend2.search_volume) / max(trend1.search_volume, trend2.search_volume)
            if volume_ratio > 0.8:  # Within 20% of each other
                bonus += 0.05
        
        return bonus


class DeduplicationEngine:
    """Main deduplication engine that handles the complete deduplication process."""
    
    def __init__(
        self,
        fuzzy_matcher: FuzzyMatcher = None,
        merge_strategy: str = "keep_best",
        quality_metrics: List[str] = None
    ):
        """
        Initialize deduplication engine.
        
        Args:
            fuzzy_matcher: Fuzzy matcher instance
            merge_strategy: Strategy for handling duplicates ("keep_best", "keep_first", "merge")
            quality_metrics: Metrics to use for determining best trend
        """
        self.fuzzy_matcher = fuzzy_matcher or FuzzyMatcher()
        self.merge_strategy = merge_strategy
        self.quality_metrics = quality_metrics or ["confidence_score", "search_volume", "source_priority"]
        
        # Source priority mapping (higher is better)
        self.source_priority = {
            "google_trends": 10,
            "trends24": 8,
            "custom": 5,
            "unknown": 1
        }
        
        self.logger = structlog.get_logger().bind(component="deduplication_engine")
    
    def deduplicate(self, trends: List[TrendData]) -> List[TrendData]:
        """
        Remove duplicates from a list of trends.
        
        Args:
            trends: List of trends to deduplicate
            
        Returns:
            Deduplicated list of trends
        """
        if not trends:
            return trends
        
        self.logger.info("Starting deduplication process", input_count=len(trends))
        
        # Find duplicates
        duplicates = self.fuzzy_matcher.find_duplicates(trends)
        
        if not duplicates:
            self.logger.info("No duplicates found")
            return trends
        
        # Group duplicates
        duplicate_groups = self._group_duplicates(duplicates)
        
        # Process each group based on merge strategy
        trends_to_remove = set()
        merged_trends = {}
        
        for group in duplicate_groups:
            if self.merge_strategy == "keep_best":
                best_trend, others = self._select_best_trend(group)
                trends_to_remove.update(id(trend) for trend in others)
            
            elif self.merge_strategy == "keep_first":
                # Keep the first trend in the original list
                first_trend = min(group, key=lambda t: trends.index(t))
                others = [t for t in group if t != first_trend]
                trends_to_remove.update(id(trend) for trend in others)
            
            elif self.merge_strategy == "merge":
                merged_trend = self._merge_trends(group)
                # Remove all original trends and add merged one
                trends_to_remove.update(id(trend) for trend in group)
                merged_trends[id(group[0])] = merged_trend
        
        # Build final list
        deduplicated_trends = []
        for trend in trends:
            trend_id = id(trend)
            
            if trend_id in trends_to_remove:
                # Check if this trend has a merged replacement
                if trend_id in merged_trends:
                    deduplicated_trends.append(merged_trends[trend_id])
            else:
                deduplicated_trends.append(trend)
        
        self.logger.info(
            "Deduplication completed",
            input_count=len(trends),
            output_count=len(deduplicated_trends),
            duplicates_removed=len(trends) - len(deduplicated_trends),
            duplicate_groups=len(duplicate_groups)
        )
        
        return deduplicated_trends
    
    def _group_duplicates(self, duplicates: List[DuplicateMatch]) -> List[List[TrendData]]:
        """Group duplicate matches into clusters of related trends."""
        # Build adjacency list
        adjacency = {}
        all_trends = set()
        
        for match in duplicates:
            trend1, trend2 = match.original_trend, match.duplicate_trend
            all_trends.add(trend1)
            all_trends.add(trend2)
            
            if trend1 not in adjacency:
                adjacency[trend1] = set()
            if trend2 not in adjacency:
                adjacency[trend2] = set()
            
            adjacency[trend1].add(trend2)
            adjacency[trend2].add(trend1)
        
        # Find connected components using DFS
        visited = set()
        groups = []
        
        for trend in all_trends:
            if trend not in visited:
                group = []
                self._dfs_group(trend, adjacency, visited, group)
                if len(group) > 1:  # Only include groups with actual duplicates
                    groups.append(group)
        
        return groups
    
    def _dfs_group(self, trend: TrendData, adjacency: Dict, visited: Set, group: List):
        """Depth-first search to find connected trends."""
        visited.add(trend)
        group.append(trend)
        
        for neighbor in adjacency.get(trend, []):
            if neighbor not in visited:
                self._dfs_group(neighbor, adjacency, visited, group)
    
    def _select_best_trend(self, trends: List[TrendData]) -> Tuple[TrendData, List[TrendData]]:
        """Select the best trend from a group of duplicates."""
        if len(trends) == 1:
            return trends[0], []
        
        # Score each trend based on quality metrics
        scored_trends = []
        for trend in trends:
            score = self._calculate_quality_score(trend)
            scored_trends.append((score, trend))
        
        # Sort by score (highest first)
        scored_trends.sort(key=lambda x: x[0], reverse=True)
        
        best_trend = scored_trends[0][1]
        others = [trend for _, trend in scored_trends[1:]]
        
        return best_trend, others
    
    def _calculate_quality_score(self, trend: TrendData) -> float:
        """Calculate quality score for a trend."""
        score = 0.0
        
        # Confidence score (0-1)
        score += trend.confidence_score * 0.3
        
        # Search volume (normalized)
        if trend.search_volume:
            # Normalize to 0-1 scale (assuming max volume of 1M)
            volume_score = min(1.0, trend.search_volume / 1000000)
            score += volume_score * 0.3
        
        # Source priority (0-1)
        source_priority = self.source_priority.get(trend.source, 1)
        max_priority = max(self.source_priority.values())
        source_score = source_priority / max_priority
        score += source_score * 0.2
        
        # Growth rate bonus
        if trend.growth_rate and trend.growth_rate > 1.0:
            growth_score = min(1.0, trend.growth_rate / 10.0)  # Normalize to 0-1
            score += growth_score * 0.1
        
        # Keyword quality (length and complexity)
        if trend.keyword:
            keyword_score = min(1.0, len(trend.keyword.split()) / 5.0)  # Prefer multi-word keywords
            score += keyword_score * 0.1
        
        return score
    
    def _merge_trends(self, trends: List[TrendData]) -> TrendData:
        """Merge multiple trends into a single trend."""
        if len(trends) == 1:
            return trends[0]
        
        # Select base trend (highest quality)
        base_trend, _ = self._select_best_trend(trends)
        
        # Merge data from other trends
        merged_search_volume = base_trend.search_volume
        merged_growth_rate = base_trend.growth_rate
        merged_confidence = base_trend.confidence_score
        
        # Aggregate search volumes
        volumes = [t.search_volume for t in trends if t.search_volume]
        if volumes:
            merged_search_volume = max(volumes)  # Use highest volume
        
        # Average growth rates
        growth_rates = [t.growth_rate for t in trends if t.growth_rate]
        if growth_rates:
            merged_growth_rate = sum(growth_rates) / len(growth_rates)
        
        # Average confidence scores
        confidences = [t.confidence_score for t in trends]
        merged_confidence = sum(confidences) / len(confidences)
        
        # Create merged trend
        merged_trend = TrendData(
            keyword=base_trend.keyword,
            search_volume=merged_search_volume,
            growth_rate=merged_growth_rate,
            region=base_trend.region,
            category=base_trend.category,
            source=f"merged_{base_trend.source}",
            timestamp=base_trend.timestamp,
            raw_data={
                "merged_from": [t.keyword for t in trends],
                "merge_count": len(trends),
                "original_sources": list(set(t.source for t in trends))
            },
            confidence_score=merged_confidence
        )
        
        return merged_trend
    
    def get_deduplication_stats(self, original_count: int, final_count: int, duplicates: List[DuplicateMatch]) -> Dict:
        """Get deduplication statistics."""
        return {
            "original_count": original_count,
            "final_count": final_count,
            "duplicates_removed": original_count - final_count,
            "deduplication_rate": (original_count - final_count) / original_count if original_count > 0 else 0,
            "duplicate_matches_found": len(duplicates),
            "merge_strategy": self.merge_strategy,
            "similarity_threshold": self.fuzzy_matcher.similarity_threshold
        }
