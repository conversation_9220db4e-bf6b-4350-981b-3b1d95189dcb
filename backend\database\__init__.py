"""
Database module for the Trend Platform.

This module provides database connection management, models, repositories,
and migration utilities using PostgreSQL with asyncpg.
"""

from .connection import DatabaseManager, get_database_pool
from .models.base_model import BaseRepository
from .models.trend_model import TrendData, TrendRepository
from .models.content_model import ContentData, ContentRepository
from .models.deployment_model import DeploymentData, DeploymentRepository
from .models.user_model import UserProfileData, UserProfileRepository

__all__ = [
    "DatabaseManager",
    "get_database_pool",
    "BaseRepository",
    "TrendData",
    "TrendRepository",
    "ContentData", 
    "ContentRepository",
    "DeploymentData",
    "DeploymentRepository",
    "UserProfileData",
    "UserProfileRepository",
]
