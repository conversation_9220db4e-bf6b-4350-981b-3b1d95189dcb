"""
Trend Scraper Module for the Trend Platform.

This module handles automated scraping of trending topics from multiple sources
including Google Trends and trends24.in, with data processing, validation,
and scoring capabilities.
"""

from .core import TrendOrchestrator
from .sources.base_scraper import BaseScraper, TrendData
from .sources.google_trends import GoogleTrendsScraper
from .sources.trends24_scraper import Trends24Scraper
from .filters import RegionFilter, CategoryFilter
from .validators import TrendValidator
from .deduplication import FuzzyMatcher
from .scoring import TrendScorer

__all__ = [
    "TrendOrchestrator",
    "BaseScraper",
    "TrendData",
    "GoogleTrendsScraper",
    "Trends24Scraper",
    "RegionFilter",
    "CategoryFilter",
    "TrendValidator",
    "FuzzyMatcher",
    "TrendScorer",
]
