"""
Coolify API client for deployment automation.

Provides interface to Coolify API for creating applications, managing deployments,
and monitoring deployment status.
"""

import asyncio
import aiohttp
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import json
import structlog

logger = structlog.get_logger()


@dataclass
class CoolifyDeployment:
    """Represents a Coolify deployment."""
    id: str
    application_id: str
    status: str
    commit_sha: Optional[str] = None
    branch: str = "main"
    created_at: Optional[datetime] = None
    finished_at: Optional[datetime] = None
    logs: List[str] = None
    error_message: Optional[str] = None
    deploy_url: Optional[str] = None
    
    def __post_init__(self):
        if self.logs is None:
            self.logs = []


class CoolifyClient:
    """Client for interacting with Coolify API."""
    
    def __init__(
        self,
        api_url: str,
        api_token: str,
        timeout: int = 300
    ):
        """
        Initialize Coolify client.
        
        Args:
            api_url: Coolify API base URL
            api_token: API authentication token
            timeout: Request timeout in seconds
        """
        self.api_url = api_url.rstrip('/')
        self.api_token = api_token
        self.timeout = timeout
        self.session = None
        
        self.logger = structlog.get_logger().bind(component="coolify_client")
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self.session is None:
            headers = {
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(headers=headers, timeout=timeout)
        return self.session
    
    async def test_connection(self) -> bool:
        """Test connection to Coolify API."""
        try:
            session = await self._get_session()
            async with session.get(f"{self.api_url}/api/v1/version") as response:
                if response.status == 200:
                    data = await response.json()
                    self.logger.info("Coolify connection successful", version=data.get("version"))
                    return True
                else:
                    self.logger.error("Coolify connection failed", status=response.status)
                    return False
        except Exception as e:
            self.logger.error("Coolify connection test failed", error=str(e))
            return False
    
    async def create_application(
        self,
        name: str,
        git_repository: str,
        git_branch: str = "main",
        build_pack: str = "static",
        environment_variables: Dict[str, str] = None,
        domains: List[str] = None
    ) -> Dict[str, Any]:
        """
        Create a new application in Coolify.
        
        Args:
            name: Application name
            git_repository: Git repository URL
            git_branch: Git branch to deploy
            build_pack: Build pack type
            environment_variables: Environment variables
            domains: Custom domains
            
        Returns:
            Application data from Coolify
        """
        session = await self._get_session()
        
        payload = {
            "name": name,
            "git_repository": git_repository,
            "git_branch": git_branch,
            "build_pack": build_pack,
            "environment_variables": environment_variables or {},
            "domains": domains or []
        }
        
        try:
            async with session.post(f"{self.api_url}/api/v1/applications", json=payload) as response:
                response.raise_for_status()
                data = await response.json()
                
                self.logger.info(
                    "Application created in Coolify",
                    app_name=name,
                    app_id=data.get("id"),
                    repository=git_repository
                )
                
                return data
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to create Coolify application", error=str(e), app_name=name)
            raise
    
    async def get_application(self, application_id: str) -> Optional[Dict[str, Any]]:
        """Get application details."""
        session = await self._get_session()
        
        try:
            async with session.get(f"{self.api_url}/api/v1/applications/{application_id}") as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    return None
                else:
                    response.raise_for_status()
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to get application", app_id=application_id, error=str(e))
            raise
    
    async def deploy_application(
        self,
        application_id: str,
        force_rebuild: bool = False,
        commit_sha: Optional[str] = None
    ) -> CoolifyDeployment:
        """
        Deploy an application.
        
        Args:
            application_id: Application ID to deploy
            force_rebuild: Whether to force rebuild
            commit_sha: Specific commit to deploy
            
        Returns:
            Deployment information
        """
        session = await self._get_session()
        
        payload = {
            "force_rebuild": force_rebuild
        }
        
        if commit_sha:
            payload["commit_sha"] = commit_sha
        
        try:
            async with session.post(
                f"{self.api_url}/api/v1/applications/{application_id}/deploy",
                json=payload
            ) as response:
                response.raise_for_status()
                data = await response.json()
                
                deployment = CoolifyDeployment(
                    id=data["id"],
                    application_id=application_id,
                    status=data.get("status", "pending"),
                    commit_sha=data.get("commit_sha"),
                    branch=data.get("branch", "main"),
                    created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None
                )
                
                self.logger.info(
                    "Deployment started",
                    app_id=application_id,
                    deployment_id=deployment.id,
                    commit_sha=commit_sha
                )
                
                return deployment
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to deploy application", app_id=application_id, error=str(e))
            raise
    
    async def get_deployment_status(self, deployment_id: str) -> CoolifyDeployment:
        """Get deployment status and details."""
        session = await self._get_session()
        
        try:
            async with session.get(f"{self.api_url}/api/v1/deployments/{deployment_id}") as response:
                response.raise_for_status()
                data = await response.json()
                
                deployment = CoolifyDeployment(
                    id=data["id"],
                    application_id=data["application_id"],
                    status=data["status"],
                    commit_sha=data.get("commit_sha"),
                    branch=data.get("branch", "main"),
                    created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
                    finished_at=datetime.fromisoformat(data["finished_at"]) if data.get("finished_at") else None,
                    error_message=data.get("error_message"),
                    deploy_url=data.get("deploy_url")
                )
                
                return deployment
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to get deployment status", deployment_id=deployment_id, error=str(e))
            raise
    
    async def get_deployment_logs(self, deployment_id: str) -> List[str]:
        """Get deployment logs."""
        session = await self._get_session()
        
        try:
            async with session.get(f"{self.api_url}/api/v1/deployments/{deployment_id}/logs") as response:
                response.raise_for_status()
                data = await response.json()
                
                return data.get("logs", [])
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to get deployment logs", deployment_id=deployment_id, error=str(e))
            raise
    
    async def cancel_deployment(self, deployment_id: str) -> bool:
        """Cancel a running deployment."""
        session = await self._get_session()
        
        try:
            async with session.post(f"{self.api_url}/api/v1/deployments/{deployment_id}/cancel") as response:
                response.raise_for_status()
                
                self.logger.info("Deployment cancelled", deployment_id=deployment_id)
                return True
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to cancel deployment", deployment_id=deployment_id, error=str(e))
            return False
    
    async def update_application_domains(
        self,
        application_id: str,
        domains: List[str]
    ) -> bool:
        """Update application domains."""
        session = await self._get_session()
        
        payload = {"domains": domains}
        
        try:
            async with session.patch(
                f"{self.api_url}/api/v1/applications/{application_id}/domains",
                json=payload
            ) as response:
                response.raise_for_status()
                
                self.logger.info(
                    "Application domains updated",
                    app_id=application_id,
                    domains=domains
                )
                return True
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to update domains", app_id=application_id, error=str(e))
            return False
    
    async def delete_application(self, application_id: str) -> bool:
        """Delete an application."""
        session = await self._get_session()
        
        try:
            async with session.delete(f"{self.api_url}/api/v1/applications/{application_id}") as response:
                response.raise_for_status()
                
                self.logger.info("Application deleted", app_id=application_id)
                return True
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to delete application", app_id=application_id, error=str(e))
            return False
    
    async def list_applications(self, limit: int = 100) -> List[Dict[str, Any]]:
        """List all applications."""
        session = await self._get_session()
        
        try:
            params = {"limit": limit}
            async with session.get(f"{self.api_url}/api/v1/applications", params=params) as response:
                response.raise_for_status()
                data = await response.json()
                
                return data.get("applications", [])
        
        except aiohttp.ClientError as e:
            self.logger.error("Failed to list applications", error=str(e))
            raise
    
    async def wait_for_deployment(
        self,
        deployment_id: str,
        timeout: int = 600,
        poll_interval: int = 10
    ) -> CoolifyDeployment:
        """
        Wait for deployment to complete.
        
        Args:
            deployment_id: Deployment ID to wait for
            timeout: Maximum time to wait in seconds
            poll_interval: Polling interval in seconds
            
        Returns:
            Final deployment status
        """
        start_time = datetime.utcnow()
        
        while True:
            deployment = await self.get_deployment_status(deployment_id)
            
            # Check if deployment is finished
            if deployment.status in ["success", "failed", "cancelled"]:
                return deployment
            
            # Check timeout
            elapsed = (datetime.utcnow() - start_time).total_seconds()
            if elapsed >= timeout:
                self.logger.warning(
                    "Deployment wait timeout",
                    deployment_id=deployment_id,
                    elapsed=elapsed
                )
                break
            
            # Wait before next poll
            await asyncio.sleep(poll_interval)
        
        # Return last known status
        return await self.get_deployment_status(deployment_id)
    
    async def close(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None
