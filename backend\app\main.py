"""
FastAPI application entry point for the Trend Platform.

This module sets up the FastAPI application with all routes, middleware,
database connections, and background tasks.
"""

from fastapi import FastAP<PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager
import logging
import structlog
from typing import Dict, Any

from .config import settings
from .dependencies import setup_dependencies
from database.connection import initialize_database, close_database, database_health_check
from .api import auth, trends, content, deploy, analytics


# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown events."""
    # Startup
    logger.info("Starting Trend Platform API", version=settings.app_version)
    
    # Initialize database
    try:
        initialize_database(
            settings.database_url,
            settings.database_min_connections,
            settings.database_max_connections
        )
        logger.info("Database initialized successfully")
        
        # Check database health
        if await database_health_check():
            logger.info("Database health check passed")
        else:
            logger.error("Database health check failed")
            raise Exception("Database is not accessible")
            
    except Exception as e:
        logger.error("Failed to initialize database", error=str(e))
        raise
    
    # Setup dependencies
    await setup_dependencies()
    logger.info("Dependencies initialized successfully")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Trend Platform API")
    await close_database()
    logger.info("Database connections closed")


def create_application() -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="Hybrid Google Trends Content Platform - Backend API",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        openapi_url="/openapi.json" if settings.debug else None,
        lifespan=lifespan
    )
    
    # Add middleware
    setup_middleware(app)
    
    # Add exception handlers
    setup_exception_handlers(app)
    
    # Include API routers
    setup_routes(app)
    
    return app


def setup_middleware(app: FastAPI):
    """Configure application middleware."""
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=settings.cors_allow_methods,
        allow_headers=settings.cors_allow_headers,
    )
    
    # Trusted host middleware for production
    if settings.environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=["*"]  # Configure with actual allowed hosts
        )
    
    # Request logging middleware
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """Log all HTTP requests."""
        start_time = structlog.get_logger().info(
            "Request started",
            method=request.method,
            url=str(request.url),
            client_ip=request.client.host if request.client else None
        )
        
        response = await call_next(request)
        
        logger.info(
            "Request completed",
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            client_ip=request.client.host if request.client else None
        )
        
        return response


def setup_exception_handlers(app: FastAPI):
    """Configure global exception handlers."""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions."""
        logger.warning(
            "HTTP exception occurred",
            status_code=exc.status_code,
            detail=exc.detail,
            url=str(request.url),
            method=request.method
        )
        return JSONResponse(
            status_code=exc.status_code,
            content={"detail": exc.detail, "status_code": exc.status_code}
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        logger.error(
            "Unhandled exception occurred",
            error=str(exc),
            url=str(request.url),
            method=request.method,
            exc_info=True
        )
        return JSONResponse(
            status_code=500,
            content={
                "detail": "Internal server error",
                "status_code": 500
            }
        )


def setup_routes(app: FastAPI):
    """Configure API routes."""
    
    # Health check endpoint
    @app.get("/health")
    async def health_check() -> Dict[str, Any]:
        """Application health check endpoint."""
        db_healthy = await database_health_check()
        
        return {
            "status": "healthy" if db_healthy else "unhealthy",
            "version": settings.app_version,
            "environment": settings.environment,
            "database": "connected" if db_healthy else "disconnected"
        }
    
    @app.get("/")
    async def root() -> Dict[str, str]:
        """Root endpoint."""
        return {
            "message": "Trend Platform API",
            "version": settings.app_version,
            "docs": "/docs" if settings.debug else "Documentation disabled in production"
        }
    
    # Include API routers with prefix
    app.include_router(
        auth.router,
        prefix=settings.api_v1_prefix,
        tags=["Authentication"]
    )
    
    app.include_router(
        trends.router,
        prefix=settings.api_v1_prefix,
        tags=["Trends"]
    )
    
    app.include_router(
        content.router,
        prefix=settings.api_v1_prefix,
        tags=["Content"]
    )
    
    app.include_router(
        deploy.router,
        prefix=settings.api_v1_prefix,
        tags=["Deployment"]
    )
    
    app.include_router(
        analytics.router,
        prefix=settings.api_v1_prefix,
        tags=["Analytics"]
    )


# Create the application instance
app = create_application()


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        workers=1 if settings.debug else settings.workers
    )
