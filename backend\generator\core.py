"""
Core orchestration module for content generation.

Coordinates the entire content generation process including AI content creation,
template rendering, asset management, and static site building.
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import structlog

from .ai_services import AIContentGenerator, OpenAIService
from .content_templates import TemplateEngine
from .asset_manager import AssetManager
from .site_builder import StaticSiteBuilder
from database.models.content_model import ContentRepository
from database.models.trend_model import TrendRepository
from app.config import settings

logger = structlog.get_logger()


class ContentOrchestrator:
    """Main orchestrator for the content generation pipeline."""
    
    def __init__(
        self,
        content_repository: ContentRepository,
        trend_repository: TrendRepository,
        config: Dict[str, Any] = None
    ):
        """
        Initialize content orchestrator.
        
        Args:
            content_repository: Database repository for content
            trend_repository: Database repository for trends
            config: Configuration dictionary
        """
        self.content_repository = content_repository
        self.trend_repository = trend_repository
        self.config = config or self._get_default_config()
        
        # Initialize AI service
        self.ai_service = self._initialize_ai_service()
        self.ai_generator = AIContentGenerator(self.ai_service, self.config.get("ai", {}))
        
        # Initialize other components
        self.template_engine = TemplateEngine()
        self.site_builder = StaticSiteBuilder(
            output_dir=self.config.get("output_dir", "/tmp/generated_sites"),
            template_engine=self.template_engine,
            config=self.config.get("site_builder", {})
        )
        
        # Statistics tracking
        self.stats = {
            "content_generated": 0,
            "sites_built": 0,
            "errors": [],
            "generation_times": []
        }
        
        self.logger = structlog.get_logger().bind(component="content_orchestrator")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration."""
        return {
            "ai": {
                "max_article_length": 2000,
                "temperature": 0.7,
                "model": "gpt-3.5-turbo"
            },
            "output_dir": "/tmp/generated_sites",
            "site_builder": {
                "site_name": "Trend Platform",
                "base_url": "",
                "git_enabled": True
            },
            "content": {
                "generate_images": True,
                "generate_code_snippets": True,
                "optimize_images": True
            }
        }
    
    def _initialize_ai_service(self):
        """Initialize AI service based on configuration."""
        return OpenAIService(
            api_key=settings.openai_api_key,
            base_url=settings.openai_base_url
        )
    
    async def generate_content_for_trend(
        self,
        trend_id: str,
        regenerate_sections: List[str] = None,
        force_regenerate: bool = False
    ) -> Dict[str, Any]:
        """
        Generate complete content for a trend.
        
        Args:
            trend_id: ID of the trend to generate content for
            regenerate_sections: Specific sections to regenerate
            force_regenerate: Whether to force regeneration of existing content
            
        Returns:
            Generation results and metadata
        """
        start_time = datetime.utcnow()
        
        self.logger.info("Starting content generation", trend_id=trend_id)
        
        try:
            # Get trend data
            trend_data = await self.trend_repository.get_by_id(trend_id)
            if not trend_data:
                raise ValueError(f"Trend not found: {trend_id}")
            
            # Check if content already exists
            existing_content = await self.content_repository.get_by_trend_id(trend_id)
            if existing_content and not force_regenerate and not regenerate_sections:
                self.logger.info("Content already exists", trend_id=trend_id)
                return {
                    "success": True,
                    "content_id": existing_content["id"],
                    "action": "existing",
                    "trend_id": trend_id
                }
            
            # Generate content
            content_data = await self._generate_content_data(trend_data, regenerate_sections)
            
            # Store content in database
            content_id = await self._store_content(trend_data, content_data, existing_content)
            
            # Generate static site
            site_result = await self._generate_static_site(trend_data, content_data)
            
            # Calculate generation time
            generation_time = (datetime.utcnow() - start_time).total_seconds()
            self.stats["generation_times"].append(generation_time)
            self.stats["content_generated"] += 1
            
            if site_result.get("success"):
                self.stats["sites_built"] += 1
            
            result = {
                "success": True,
                "content_id": content_id,
                "trend_id": trend_id,
                "action": "regenerated" if existing_content else "created",
                "generation_time": generation_time,
                "site_result": site_result,
                "content_data": {
                    "title": content_data.get("title"),
                    "word_count": content_data.get("word_count"),
                    "has_image": bool(content_data.get("hero_image")),
                    "has_code": bool(content_data.get("code_snippet"))
                }
            }
            
            self.logger.info(
                "Content generation completed",
                trend_id=trend_id,
                content_id=content_id,
                generation_time=generation_time,
                site_built=site_result.get("success", False)
            )
            
            return result
        
        except Exception as e:
            error_msg = f"Content generation failed for trend {trend_id}: {str(e)}"
            self.logger.error("Content generation failed", trend_id=trend_id, error=str(e))
            self.stats["errors"].append(error_msg)
            
            return {
                "success": False,
                "error": str(e),
                "trend_id": trend_id,
                "generation_time": (datetime.utcnow() - start_time).total_seconds()
            }
    
    async def _generate_content_data(
        self,
        trend_data: Dict[str, Any],
        regenerate_sections: List[str] = None
    ) -> Dict[str, Any]:
        """Generate all content data for a trend."""
        
        # Prepare context
        context = {
            "search_volume": trend_data.get("search_volume"),
            "growth_rate": trend_data.get("growth_rate"),
            "region": trend_data.get("region"),
            "source": trend_data.get("source"),
            "generation_time": datetime.utcnow()
        }
        
        # Generate main article content
        article_data = await self.ai_generator.generate_article(
            keyword=trend_data["keyword"],
            category=trend_data["category"],
            region=trend_data["region"],
            context=context
        )
        
        content_data = {
            "title": article_data["title"],
            "content": article_data["content"],
            "meta_description": article_data["meta_description"],
            "code_snippet": article_data.get("code_snippet"),
            "word_count": article_data["word_count"],
            "generation_metadata": article_data["generation_metadata"]
        }
        
        # Generate hero image if enabled and requested
        if (self.config.get("content", {}).get("generate_images", True) and
            (not regenerate_sections or "image" in regenerate_sections)):
            
            try:
                hero_image = await self.ai_generator.generate_hero_image(
                    keyword=trend_data["keyword"],
                    category=trend_data["category"],
                    article_content=article_data["content"]
                )
                
                if hero_image:
                    content_data["hero_image"] = hero_image
                    content_data["hero_image_url"] = hero_image["image_url"]
            
            except Exception as e:
                self.logger.warning("Hero image generation failed", error=str(e))
        
        return content_data
    
    async def _store_content(
        self,
        trend_data: Dict[str, Any],
        content_data: Dict[str, Any],
        existing_content: Dict[str, Any] = None
    ) -> str:
        """Store generated content in database."""
        
        # Prepare content record
        content_record = {
            "trend_id": trend_data["id"],
            "title": content_data["title"],
            "description": content_data["meta_description"],
            "body": content_data["content"],
            "meta_tags": {
                "description": content_data["meta_description"],
                "keywords": trend_data["keyword"],
                "category": trend_data["category"]
            },
            "hero_image_url": content_data.get("hero_image_url"),
            "code_snippet": content_data.get("code_snippet", {}).get("code") if content_data.get("code_snippet") else None,
            "code_language": content_data.get("code_snippet", {}).get("language") if content_data.get("code_snippet") else None,
            "word_count": content_data["word_count"],
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        if existing_content:
            # Update existing content
            await self.content_repository.update(existing_content["id"], content_record)
            content_id = existing_content["id"]
            self.logger.info("Content updated", content_id=content_id, trend_id=trend_data["id"])
        else:
            # Create new content
            content_id = await self.content_repository.create(content_record)
            self.logger.info("Content created", content_id=content_id, trend_id=trend_data["id"])
        
        return content_id
    
    async def _generate_static_site(
        self,
        trend_data: Dict[str, Any],
        content_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate static site for the content."""
        
        try:
            site_result = await self.site_builder.build_site(
                trend_data=trend_data,
                content_data=content_data,
                force_rebuild=False
            )
            
            return site_result
        
        except Exception as e:
            self.logger.error("Static site generation failed", error=str(e))
            return {
                "success": False,
                "error": str(e)
            }
    
    async def batch_generate_content(
        self,
        trend_ids: List[str],
        max_concurrent: int = 3
    ) -> Dict[str, Any]:
        """
        Generate content for multiple trends concurrently.
        
        Args:
            trend_ids: List of trend IDs to process
            max_concurrent: Maximum concurrent generations
            
        Returns:
            Batch processing results
        """
        self.logger.info("Starting batch content generation", trend_count=len(trend_ids))
        
        # Create semaphore to limit concurrency
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def generate_with_semaphore(trend_id: str):
            async with semaphore:
                return await self.generate_content_for_trend(trend_id)
        
        # Execute all generations
        tasks = [generate_with_semaphore(trend_id) for trend_id in trend_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        successful = 0
        failed = 0
        errors = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed += 1
                errors.append(f"Trend {trend_ids[i]}: {str(result)}")
            elif result.get("success"):
                successful += 1
            else:
                failed += 1
                errors.append(f"Trend {trend_ids[i]}: {result.get('error', 'Unknown error')}")
        
        batch_result = {
            "total_trends": len(trend_ids),
            "successful": successful,
            "failed": failed,
            "success_rate": successful / len(trend_ids) if trend_ids else 0,
            "errors": errors,
            "results": [r for r in results if not isinstance(r, Exception)]
        }
        
        self.logger.info(
            "Batch content generation completed",
            total=len(trend_ids),
            successful=successful,
            failed=failed,
            success_rate=f"{batch_result['success_rate']:.2%}"
        )
        
        return batch_result
    
    async def cleanup_old_content(self, days_to_keep: int = 30) -> Dict[str, Any]:
        """Clean up old generated content and sites."""
        self.logger.info("Starting content cleanup", days_to_keep=days_to_keep)
        
        # This would implement cleanup logic for old content
        # For now, return placeholder
        return {
            "content_cleaned": 0,
            "sites_cleaned": 0,
            "space_freed": 0
        }
    
    async def get_generation_stats(self) -> Dict[str, Any]:
        """Get content generation statistics."""
        avg_generation_time = (
            sum(self.stats["generation_times"]) / len(self.stats["generation_times"])
            if self.stats["generation_times"] else 0
        )
        
        return {
            "content_generated": self.stats["content_generated"],
            "sites_built": self.stats["sites_built"],
            "error_count": len(self.stats["errors"]),
            "avg_generation_time": avg_generation_time,
            "recent_errors": self.stats["errors"][-5:],  # Last 5 errors
            "success_rate": (
                self.stats["content_generated"] / 
                (self.stats["content_generated"] + len(self.stats["errors"]))
                if (self.stats["content_generated"] + len(self.stats["errors"])) > 0 else 0
            )
        }
    
    async def close(self):
        """Close all connections and cleanup resources."""
        if hasattr(self.ai_generator, 'close'):
            await self.ai_generator.close()
        
        self.logger.info("Content orchestrator closed")
