"""
Deployment tracking and monitoring system.

Tracks deployment status, monitors progress, and provides deployment
analytics and reporting capabilities.
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from enum import Enum
import structlog

from database.models.deployment_model import DeploymentRepository
from .coolify_client import CoolifyClient, CoolifyDeployment

logger = structlog.get_logger()


class DeploymentStatus(Enum):
    """Deployment status enumeration."""
    PENDING = "pending"
    BUILDING = "building"
    SUCCESS = "success"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DeploymentTracker:
    """Tracks and monitors deployment progress."""
    
    def __init__(
        self,
        deployment_repository: DeploymentRepository,
        coolify_client: CoolifyClient,
        config: Dict[str, Any] = None
    ):
        """
        Initialize deployment tracker.
        
        Args:
            deployment_repository: Database repository for deployments
            coolify_client: Coolify API client
            config: Tracker configuration
        """
        self.deployment_repo = deployment_repository
        self.coolify_client = coolify_client
        self.config = config or {}
        
        # Monitoring configuration
        self.poll_interval = self.config.get("poll_interval", 30)  # seconds
        self.max_deployment_time = self.config.get("max_deployment_time", 1800)  # 30 minutes
        self.retry_failed_deployments = self.config.get("retry_failed_deployments", True)
        self.max_retries = self.config.get("max_retries", 3)
        
        # Active monitoring tasks
        self.monitoring_tasks = {}
        
        self.logger = structlog.get_logger().bind(component="deployment_tracker")
    
    async def start_deployment_tracking(
        self,
        deployment_id: str,
        coolify_deployment_id: str,
        trend_id: str
    ) -> bool:
        """
        Start tracking a deployment.
        
        Args:
            deployment_id: Internal deployment ID
            coolify_deployment_id: Coolify deployment ID
            trend_id: Associated trend ID
            
        Returns:
            True if tracking started successfully
        """
        try:
            # Update deployment with Coolify ID
            await self.deployment_repo.update_deployment_status(
                deployment_id=deployment_id,
                status="building",
                coolify_uuid=coolify_deployment_id
            )
            
            # Start monitoring task
            task = asyncio.create_task(
                self._monitor_deployment(deployment_id, coolify_deployment_id, trend_id)
            )
            self.monitoring_tasks[deployment_id] = task
            
            self.logger.info(
                "Started deployment tracking",
                deployment_id=deployment_id,
                coolify_deployment_id=coolify_deployment_id,
                trend_id=trend_id
            )
            
            return True
        
        except Exception as e:
            self.logger.error(
                "Failed to start deployment tracking",
                deployment_id=deployment_id,
                error=str(e)
            )
            return False
    
    async def _monitor_deployment(
        self,
        deployment_id: str,
        coolify_deployment_id: str,
        trend_id: str
    ):
        """Monitor a single deployment until completion."""
        start_time = datetime.utcnow()
        last_status = None
        
        try:
            while True:
                # Get deployment status from Coolify
                coolify_deployment = await self.coolify_client.get_deployment_status(
                    coolify_deployment_id
                )
                
                # Update database if status changed
                if coolify_deployment.status != last_status:
                    await self._update_deployment_status(
                        deployment_id, coolify_deployment
                    )
                    last_status = coolify_deployment.status
                
                # Check if deployment is finished
                if coolify_deployment.status in ["success", "failed", "cancelled"]:
                    await self._handle_deployment_completion(
                        deployment_id, coolify_deployment, trend_id
                    )
                    break
                
                # Check for timeout
                elapsed = (datetime.utcnow() - start_time).total_seconds()
                if elapsed > self.max_deployment_time:
                    await self._handle_deployment_timeout(
                        deployment_id, coolify_deployment_id
                    )
                    break
                
                # Wait before next check
                await asyncio.sleep(self.poll_interval)
        
        except Exception as e:
            self.logger.error(
                "Deployment monitoring failed",
                deployment_id=deployment_id,
                error=str(e)
            )
            
            # Mark deployment as failed
            await self.deployment_repo.update_deployment_status(
                deployment_id=deployment_id,
                status="failed",
                error_message=f"Monitoring error: {str(e)}"
            )
        
        finally:
            # Remove from active monitoring
            self.monitoring_tasks.pop(deployment_id, None)
    
    async def _update_deployment_status(
        self,
        deployment_id: str,
        coolify_deployment: CoolifyDeployment
    ):
        """Update deployment status in database."""
        
        # Calculate progress based on status
        progress_map = {
            "pending": 0,
            "building": 50,
            "success": 100,
            "failed": 0,
            "cancelled": 0
        }
        
        progress = progress_map.get(coolify_deployment.status, 0)
        
        # Get deployment logs
        try:
            logs = await self.coolify_client.get_deployment_logs(coolify_deployment.id)
            log_content = "\n".join(logs) if logs else None
        except:
            log_content = None
        
        # Update database
        await self.deployment_repo.update_deployment_status(
            deployment_id=deployment_id,
            status=coolify_deployment.status,
            progress=progress,
            error_message=coolify_deployment.error_message,
            deploy_url=coolify_deployment.deploy_url
        )
        
        # Update build log if available
        if log_content:
            await self.deployment_repo.update_build_log(
                deployment_id=deployment_id,
                log_content=log_content,
                append=False
            )
        
        self.logger.info(
            "Deployment status updated",
            deployment_id=deployment_id,
            status=coolify_deployment.status,
            progress=progress
        )
    
    async def _handle_deployment_completion(
        self,
        deployment_id: str,
        coolify_deployment: CoolifyDeployment,
        trend_id: str
    ):
        """Handle deployment completion."""
        
        if coolify_deployment.status == "success":
            # Calculate build duration
            await self.deployment_repo.calculate_build_duration(deployment_id)
            
            # Update trend status if deployment was successful
            from database.models.trend_model import TrendRepository
            # This would need to be injected or retrieved
            # trend_repo = TrendRepository(...)
            # await trend_repo.update(trend_id, {"status": "deployed"})
            
            self.logger.info(
                "Deployment completed successfully",
                deployment_id=deployment_id,
                deploy_url=coolify_deployment.deploy_url
            )
        
        elif coolify_deployment.status == "failed":
            # Handle failed deployment
            await self._handle_deployment_failure(
                deployment_id, coolify_deployment, trend_id
            )
        
        else:  # cancelled
            self.logger.info(
                "Deployment was cancelled",
                deployment_id=deployment_id
            )
    
    async def _handle_deployment_failure(
        self,
        deployment_id: str,
        coolify_deployment: CoolifyDeployment,
        trend_id: str
    ):
        """Handle deployment failure."""
        
        self.logger.error(
            "Deployment failed",
            deployment_id=deployment_id,
            error=coolify_deployment.error_message
        )
        
        # Check if we should retry
        if self.retry_failed_deployments:
            deployment_data = await self.deployment_repo.get_by_id(deployment_id)
            retry_count = deployment_data.get("retry_count", 0)
            
            if retry_count < self.max_retries:
                self.logger.info(
                    "Scheduling deployment retry",
                    deployment_id=deployment_id,
                    retry_count=retry_count + 1
                )
                
                # Schedule retry (this would integrate with the deployment orchestrator)
                # await self._schedule_deployment_retry(deployment_id, trend_id)
    
    async def _handle_deployment_timeout(
        self,
        deployment_id: str,
        coolify_deployment_id: str
    ):
        """Handle deployment timeout."""
        
        self.logger.warning(
            "Deployment timeout",
            deployment_id=deployment_id,
            max_time=self.max_deployment_time
        )
        
        # Try to cancel the deployment
        try:
            await self.coolify_client.cancel_deployment(coolify_deployment_id)
        except:
            pass
        
        # Update status
        await self.deployment_repo.update_deployment_status(
            deployment_id=deployment_id,
            status="failed",
            error_message=f"Deployment timeout after {self.max_deployment_time} seconds"
        )
    
    async def get_deployment_metrics(
        self,
        days: int = 7
    ) -> Dict[str, Any]:
        """Get deployment metrics for the specified period."""
        
        # Get recent deployments
        recent_deployments = await self.deployment_repo.get_recent_deployments(
            days=days, limit=1000
        )
        
        # Calculate metrics
        total_deployments = len(recent_deployments)
        successful_deployments = sum(
            1 for d in recent_deployments if d["status"] == "success"
        )
        failed_deployments = sum(
            1 for d in recent_deployments if d["status"] == "failed"
        )
        
        # Calculate average build time
        build_times = [
            d["build_duration"] for d in recent_deployments 
            if d.get("build_duration")
        ]
        avg_build_time = sum(build_times) / len(build_times) if build_times else 0
        
        # Success rate
        success_rate = successful_deployments / total_deployments if total_deployments > 0 else 0
        
        # Deployments by status
        status_counts = {}
        for deployment in recent_deployments:
            status = deployment["status"]
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "period_days": days,
            "total_deployments": total_deployments,
            "successful_deployments": successful_deployments,
            "failed_deployments": failed_deployments,
            "success_rate": success_rate,
            "average_build_time_seconds": avg_build_time,
            "deployments_by_status": status_counts,
            "active_monitoring_tasks": len(self.monitoring_tasks)
        }
    
    async def get_active_deployments(self) -> List[Dict[str, Any]]:
        """Get currently active deployments."""
        active_deployments = await self.deployment_repo.get_deployments_by_status("building")
        
        # Add monitoring status
        for deployment in active_deployments:
            deployment["is_monitored"] = deployment["id"] in self.monitoring_tasks
        
        return active_deployments
    
    async def cancel_deployment(self, deployment_id: str) -> bool:
        """Cancel a deployment."""
        try:
            # Get deployment data
            deployment = await self.deployment_repo.get_by_id(deployment_id)
            if not deployment:
                return False
            
            coolify_uuid = deployment.get("coolify_deployment_uuid")
            if coolify_uuid:
                # Cancel in Coolify
                await self.coolify_client.cancel_deployment(coolify_uuid)
            
            # Update status
            await self.deployment_repo.update_deployment_status(
                deployment_id=deployment_id,
                status="cancelled"
            )
            
            # Stop monitoring
            if deployment_id in self.monitoring_tasks:
                self.monitoring_tasks[deployment_id].cancel()
                del self.monitoring_tasks[deployment_id]
            
            self.logger.info("Deployment cancelled", deployment_id=deployment_id)
            return True
        
        except Exception as e:
            self.logger.error("Failed to cancel deployment", deployment_id=deployment_id, error=str(e))
            return False
    
    async def cleanup_old_deployments(self, days: int = 90) -> int:
        """Clean up old deployment records."""
        return await self.deployment_repo.cleanup_old_deployments(days)
    
    async def stop_all_monitoring(self):
        """Stop all active monitoring tasks."""
        for task in self.monitoring_tasks.values():
            task.cancel()
        
        # Wait for tasks to complete
        if self.monitoring_tasks:
            await asyncio.gather(*self.monitoring_tasks.values(), return_exceptions=True)
        
        self.monitoring_tasks.clear()
        self.logger.info("All monitoring tasks stopped")
