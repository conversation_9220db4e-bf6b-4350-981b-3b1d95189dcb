"""
Trends API endpoints.

Handles trend management operations including listing, approval,
rejection, and trend-related analytics.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from pydantic import BaseModel, Field
from datetime import datetime
import structlog

from ..dependencies import (
    get_trend_repository,
    get_current_active_user,
    require_editor,
    require_admin,
    get_pagination_params,
    PaginationParams
)
from database.models.trend_model import TrendRepository, TrendData

logger = structlog.get_logger()

router = APIRouter(prefix="/trends")


class TrendResponse(BaseModel):
    """Trend response model."""
    id: str
    keyword: str
    slug: str
    status: str
    region: str
    category: str
    search_volume: Optional[int]
    growth_rate: Optional[float]
    score: Optional[float]
    source: str
    created_at: datetime
    updated_at: datetime
    expire_at: Optional[datetime]
    deployed_at: Optional[datetime]
    ads_enabled: bool


class TrendListResponse(BaseModel):
    """Paginated trend list response."""
    data: List[TrendResponse]
    pagination: Dict[str, Any]


class TrendApprovalRequest(BaseModel):
    """Trend approval request."""
    trend_ids: List[str] = Field(..., min_items=1, max_items=50)


class TrendCreateRequest(BaseModel):
    """Manual trend creation request."""
    keyword: str = Field(..., min_length=1, max_length=255)
    region: str = Field(..., min_length=2, max_length=10)
    category: str = Field(..., min_length=1, max_length=100)
    source: str = Field(default="manual")
    search_volume: Optional[int] = Field(None, ge=0)
    growth_rate: Optional[float] = Field(None, ge=0.0)


@router.get("/", response_model=TrendListResponse)
async def list_trends(
    status_filter: Optional[str] = Query(None, description="Filter by status"),
    region: Optional[str] = Query(None, description="Filter by region"),
    category: Optional[str] = Query(None, description="Filter by category"),
    search: Optional[str] = Query(None, description="Search in keywords"),
    pagination: PaginationParams = Depends(get_pagination_params),
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """List trends with filtering and pagination."""
    logger.info(
        "List trends request",
        user_id=current_user["id"],
        status_filter=status_filter,
        region=region,
        category=category,
        search=search,
        page=pagination.page,
        page_size=pagination.page_size
    )
    
    # Build filters
    filters = {}
    if status_filter:
        filters["status"] = status_filter
    if region:
        filters["region"] = region
    if category:
        filters["category"] = category
    
    try:
        if search:
            # Use search functionality
            trends = await trend_repo.search_trends(search, pagination.page_size)
            # For search, we don't have pagination info, so create a simple response
            return TrendListResponse(
                data=[TrendResponse(**trend) for trend in trends],
                pagination={
                    "page": 1,
                    "page_size": len(trends),
                    "total_count": len(trends),
                    "total_pages": 1
                }
            )
        else:
            # Use regular pagination
            result = await trend_repo.list_with_pagination(
                page=pagination.page,
                page_size=pagination.page_size,
                filters=filters,
                order_by="score DESC NULLS LAST, created_at DESC"
            )
            
            return TrendListResponse(
                data=[TrendResponse(**trend) for trend in result["data"]],
                pagination=result["pagination"]
            )
    
    except Exception as e:
        logger.error("Failed to list trends", error=str(e), user_id=current_user["id"])
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve trends"
        )


@router.get("/pending", response_model=List[TrendResponse])
async def get_pending_trends(
    limit: int = Query(50, ge=1, le=100),
    current_user: Dict[str, Any] = Depends(require_editor()),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """Get pending trends for approval."""
    logger.info("Get pending trends", user_id=current_user["id"], limit=limit)
    
    try:
        trends = await trend_repo.get_pending_trends(limit)
        return [TrendResponse(**trend) for trend in trends]
    
    except Exception as e:
        logger.error("Failed to get pending trends", error=str(e), user_id=current_user["id"])
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve pending trends"
        )


@router.get("/live", response_model=List[TrendResponse])
async def get_live_trends(
    region: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """Get currently live trends (public endpoint)."""
    logger.info("Get live trends", region=region, category=category)
    
    try:
        trends = await trend_repo.get_live_trends(region, category)
        return [TrendResponse(**trend) for trend in trends]
    
    except Exception as e:
        logger.error("Failed to get live trends", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve live trends"
        )


@router.get("/categories", response_model=List[Dict[str, Any]])
async def get_trending_categories(
    region: Optional[str] = Query(None),
    days: int = Query(7, ge=1, le=30),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """Get trending categories with statistics."""
    logger.info("Get trending categories", region=region, days=days)
    
    try:
        categories = await trend_repo.get_trending_categories(region, days)
        return categories
    
    except Exception as e:
        logger.error("Failed to get trending categories", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve trending categories"
        )


@router.get("/{trend_id}", response_model=TrendResponse)
async def get_trend(
    trend_id: str,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """Get specific trend by ID."""
    logger.info("Get trend", trend_id=trend_id, user_id=current_user["id"])
    
    try:
        trend = await trend_repo.get_by_id(trend_id)
        if not trend:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trend not found"
            )
        
        return TrendResponse(**trend)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get trend", error=str(e), trend_id=trend_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve trend"
        )


@router.post("/", response_model=TrendResponse)
async def create_trend(
    request: TrendCreateRequest,
    current_user: Dict[str, Any] = Depends(require_editor()),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """Create a new trend manually."""
    logger.info("Create trend", keyword=request.keyword, user_id=current_user["id"])
    
    try:
        # Check if trend already exists for this keyword and region
        existing = await trend_repo.get_by_keyword_and_region(request.keyword, request.region)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Trend already exists for this keyword and region"
            )
        
        # Generate slug from keyword
        slug = request.keyword.lower().replace(" ", "-").replace("_", "-")
        
        # Create trend data
        trend_data = {
            "keyword": request.keyword,
            "slug": slug,
            "status": "pending",
            "region": request.region,
            "category": request.category,
            "source": request.source,
            "search_volume": request.search_volume,
            "growth_rate": request.growth_rate,
            "created_by": current_user["id"]
        }
        
        trend_id = await trend_repo.create(trend_data)
        created_trend = await trend_repo.get_by_id(trend_id)
        
        logger.info("Trend created", trend_id=trend_id, keyword=request.keyword)
        return TrendResponse(**created_trend)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create trend", error=str(e), keyword=request.keyword)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create trend"
        )


@router.post("/approve", response_model=Dict[str, Any])
async def approve_trends(
    request: TrendApprovalRequest,
    current_user: Dict[str, Any] = Depends(require_editor()),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """Approve multiple trends."""
    logger.info(
        "Approve trends",
        trend_ids=request.trend_ids,
        user_id=current_user["id"],
        count=len(request.trend_ids)
    )
    
    approved_count = 0
    failed_ids = []
    
    for trend_id in request.trend_ids:
        try:
            success = await trend_repo.approve_trend(trend_id, current_user["id"])
            if success:
                approved_count += 1
            else:
                failed_ids.append(trend_id)
        except Exception as e:
            logger.error("Failed to approve trend", error=str(e), trend_id=trend_id)
            failed_ids.append(trend_id)
    
    logger.info(
        "Trends approval completed",
        approved_count=approved_count,
        failed_count=len(failed_ids),
        user_id=current_user["id"]
    )
    
    return {
        "approved_count": approved_count,
        "failed_count": len(failed_ids),
        "failed_ids": failed_ids,
        "message": f"Approved {approved_count} out of {len(request.trend_ids)} trends"
    }


@router.post("/reject", response_model=Dict[str, Any])
async def reject_trends(
    request: TrendApprovalRequest,
    current_user: Dict[str, Any] = Depends(require_editor()),
    trend_repo: TrendRepository = Depends(get_trend_repository)
):
    """Reject multiple trends."""
    logger.info(
        "Reject trends",
        trend_ids=request.trend_ids,
        user_id=current_user["id"],
        count=len(request.trend_ids)
    )
    
    rejected_count = 0
    failed_ids = []
    
    for trend_id in request.trend_ids:
        try:
            success = await trend_repo.reject_trend(trend_id, current_user["id"])
            if success:
                rejected_count += 1
            else:
                failed_ids.append(trend_id)
        except Exception as e:
            logger.error("Failed to reject trend", error=str(e), trend_id=trend_id)
            failed_ids.append(trend_id)
    
    logger.info(
        "Trends rejection completed",
        rejected_count=rejected_count,
        failed_count=len(failed_ids),
        user_id=current_user["id"]
    )
    
    return {
        "rejected_count": rejected_count,
        "failed_count": len(failed_ids),
        "failed_ids": failed_ids,
        "message": f"Rejected {rejected_count} out of {len(request.trend_ids)} trends"
    }
