"""
Deployment model and repository implementation.

Handles all database operations related to deployments including
CRUD operations, status tracking, and deployment-specific queries.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field
import asyncpg
from .base_model import BaseRepository
import logging

logger = logging.getLogger(__name__)


class DeploymentData(BaseModel):
    """Pydantic model for deployment data validation."""
    id: Optional[str] = None
    trend_id: str
    content_id: Optional[str] = None
    status: str = Field(default='pending', pattern='^(pending|building|success|failed|cancelled)$')
    coolify_deployment_uuid: Optional[str] = None
    build_log: Optional[str] = None
    deploy_url: Optional[str] = None
    error_message: Optional[str] = None
    progress: int = Field(default=0, ge=0, le=100)
    build_duration: Optional[int] = Field(None, ge=0)  # seconds
    created_at: Optional[datetime] = None
    deployed_at: Optional[datetime] = None
    triggered_by: Optional[str] = None


class DeploymentRepository(BaseRepository[DeploymentData]):
    """Repository for deployment-related database operations."""
    
    def __init__(self, connection_pool: asyncpg.Pool):
        super().__init__(connection_pool, 'deployments')
    
    async def get_by_trend_id(self, trend_id: str) -> List[Dict[str, Any]]:
        """Get all deployments for a specific trend."""
        query = """
            SELECT * FROM deployments 
            WHERE trend_id = $1 
            ORDER BY created_at DESC
        """
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, trend_id)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get deployments by trend_id {trend_id}: {e}")
            raise
    
    async def get_latest_deployment(self, trend_id: str) -> Optional[Dict[str, Any]]:
        """Get the latest deployment for a trend."""
        query = """
            SELECT * FROM deployments 
            WHERE trend_id = $1 
            ORDER BY created_at DESC 
            LIMIT 1
        """
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, trend_id)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Failed to get latest deployment for trend {trend_id}: {e}")
            raise
    
    async def get_successful_deployment(self, trend_id: str) -> Optional[Dict[str, Any]]:
        """Get the most recent successful deployment for a trend."""
        query = """
            SELECT * FROM deployments 
            WHERE trend_id = $1 AND status = 'success'
            ORDER BY deployed_at DESC 
            LIMIT 1
        """
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, trend_id)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Failed to get successful deployment for trend {trend_id}: {e}")
            raise
    
    async def get_deployments_by_status(self, status: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get deployments filtered by status."""
        query = """
            SELECT 
                d.*,
                t.keyword,
                t.slug as trend_slug,
                t.region,
                t.category
            FROM deployments d
            JOIN trends t ON d.trend_id = t.id
            WHERE d.status = $1
            ORDER BY d.created_at DESC
            LIMIT $2
        """
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, status, limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get deployments by status {status}: {e}")
            raise
    
    async def get_pending_deployments(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get pending deployments."""
        return await self.get_deployments_by_status('pending', limit)
    
    async def get_failed_deployments(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get failed deployments."""
        return await self.get_deployments_by_status('failed', limit)
    
    async def get_recent_deployments(self, days: int = 7, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent deployments."""
        query = """
            SELECT 
                d.*,
                t.keyword,
                t.slug as trend_slug,
                t.region,
                t.category
            FROM deployments d
            JOIN trends t ON d.trend_id = t.id
            WHERE d.created_at >= NOW() - INTERVAL '%s days'
            ORDER BY d.created_at DESC
            LIMIT $1
        """ % days
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get recent deployments: {e}")
            raise
    
    async def update_deployment_status(
        self, 
        deployment_id: str, 
        status: str, 
        progress: int = None,
        error_message: str = None,
        deploy_url: str = None,
        coolify_uuid: str = None
    ) -> bool:
        """Update deployment status and related fields."""
        update_data = {
            'status': status,
            'updated_at': datetime.utcnow()
        }
        
        if progress is not None:
            update_data['progress'] = progress
        
        if error_message is not None:
            update_data['error_message'] = error_message
        
        if deploy_url is not None:
            update_data['deploy_url'] = deploy_url
        
        if coolify_uuid is not None:
            update_data['coolify_deployment_uuid'] = coolify_uuid
        
        # Set deployed_at timestamp for successful deployments
        if status == 'success':
            update_data['deployed_at'] = datetime.utcnow()
        
        try:
            result = await self.update(deployment_id, update_data)
            if result:
                logger.info(f"Updated deployment {deployment_id} status to {status}")
            return result
        except Exception as e:
            logger.error(f"Failed to update deployment status {deployment_id}: {e}")
            raise
    
    async def update_build_log(self, deployment_id: str, log_content: str, append: bool = True) -> bool:
        """Update deployment build log."""
        if append:
            # Append to existing log
            query = """
                UPDATE deployments 
                SET build_log = COALESCE(build_log, '') || $2,
                    updated_at = NOW()
                WHERE id = $1
            """
        else:
            # Replace entire log
            query = """
                UPDATE deployments 
                SET build_log = $2,
                    updated_at = NOW()
                WHERE id = $1
            """
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.execute(query, deployment_id, log_content)
                updated = result.split()[-1] == '1'
                if updated:
                    logger.info(f"Updated build log for deployment {deployment_id}")
                return updated
        except Exception as e:
            logger.error(f"Failed to update build log for deployment {deployment_id}: {e}")
            raise
    
    async def calculate_build_duration(self, deployment_id: str) -> bool:
        """Calculate and update build duration for completed deployment."""
        query = """
            UPDATE deployments 
            SET build_duration = EXTRACT(EPOCH FROM (deployed_at - created_at))::INTEGER,
                updated_at = NOW()
            WHERE id = $1 AND deployed_at IS NOT NULL
        """
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.execute(query, deployment_id)
                updated = result.split()[-1] == '1'
                if updated:
                    logger.info(f"Calculated build duration for deployment {deployment_id}")
                return updated
        except Exception as e:
            logger.error(f"Failed to calculate build duration for deployment {deployment_id}: {e}")
            raise
    
    async def get_deployment_stats(self) -> Dict[str, Any]:
        """Get deployment statistics."""
        stats_query = """
            SELECT 
                COUNT(*) as total_deployments,
                COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_deployments,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_deployments,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_deployments,
                COUNT(CASE WHEN status = 'building' THEN 1 END) as building_deployments,
                AVG(build_duration) as avg_build_duration,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as recent_deployments_count
            FROM deployments
        """
        
        status_query = """
            SELECT 
                status,
                COUNT(*) as count
            FROM deployments
            GROUP BY status
            ORDER BY count DESC
        """
        
        try:
            async with self.pool.acquire() as conn:
                # Get general stats
                stats_row = await conn.fetchrow(stats_query)
                stats = dict(stats_row) if stats_row else {}
                
                # Get status breakdown
                status_rows = await conn.fetch(status_query)
                stats['deployments_by_status'] = {
                    row['status']: row['count'] 
                    for row in status_rows
                }
                
                return stats
        except Exception as e:
            logger.error(f"Failed to get deployment stats: {e}")
            raise
    
    async def cleanup_old_deployments(self, days: int = 90) -> int:
        """Clean up old deployment records (keep only recent ones)."""
        query = """
            DELETE FROM deployments 
            WHERE created_at < NOW() - INTERVAL '%s days'
            AND status IN ('failed', 'cancelled')
        """ % days
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.execute(query)
                deleted_count = int(result.split()[-1])
                if deleted_count > 0:
                    logger.info(f"Cleaned up {deleted_count} old deployment records")
                return deleted_count
        except Exception as e:
            logger.error(f"Failed to cleanup old deployments: {e}")
            raise
    
    async def get_deployment_with_trend(self, deployment_id: str) -> Optional[Dict[str, Any]]:
        """Get deployment with associated trend information."""
        query = """
            SELECT 
                d.*,
                t.keyword,
                t.slug as trend_slug,
                t.status as trend_status,
                t.region,
                t.category
            FROM deployments d
            JOIN trends t ON d.trend_id = t.id
            WHERE d.id = $1
        """
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, deployment_id)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Failed to get deployment with trend {deployment_id}: {e}")
            raise
