"""
Content templates and template engine for static site generation.

Provides Jinja2-based templating system for generating HTML pages,
CSS styles, and other static assets.
"""

import os
from typing import Dict, Any, Optional, List
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template, select_autoescape
from datetime import datetime
import structlog

logger = structlog.get_logger()


class ContentTemplate:
    """Represents a content template with metadata."""
    
    def __init__(
        self,
        name: str,
        template_path: str,
        template_type: str = "html",
        metadata: Dict[str, Any] = None
    ):
        self.name = name
        self.template_path = template_path
        self.template_type = template_type
        self.metadata = metadata or {}
        self.template = None
    
    def load_template(self, env: Environment) -> Template:
        """Load the Jinja2 template."""
        if self.template is None:
            self.template = env.get_template(self.template_path)
        return self.template
    
    def render(self, env: Environment, context: Dict[str, Any]) -> str:
        """Render the template with given context."""
        template = self.load_template(env)
        return template.render(**context)


class TemplateEngine:
    """Jinja2-based template engine for content generation."""
    
    def __init__(self, template_dir: str = None):
        """
        Initialize template engine.
        
        Args:
            template_dir: Directory containing template files
        """
        self.template_dir = template_dir or self._get_default_template_dir()
        self.env = self._create_environment()
        self.templates = {}
        self.logger = structlog.get_logger().bind(component="template_engine")
        
        # Load default templates
        self._load_default_templates()
    
    def _get_default_template_dir(self) -> str:
        """Get default template directory."""
        current_dir = Path(__file__).parent
        return str(current_dir / "templates")
    
    def _create_environment(self) -> Environment:
        """Create Jinja2 environment."""
        loader = FileSystemLoader(self.template_dir)
        env = Environment(
            loader=loader,
            autoescape=select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # Add custom filters
        env.filters['datetime'] = self._datetime_filter
        env.filters['truncate_words'] = self._truncate_words_filter
        env.filters['slug'] = self._slug_filter
        
        # Add global functions
        env.globals['now'] = datetime.utcnow
        env.globals['year'] = datetime.utcnow().year
        
        return env
    
    def _load_default_templates(self):
        """Load default template definitions."""
        self.templates = {
            "article_page": ContentTemplate(
                name="article_page",
                template_path="article.html",
                template_type="html",
                metadata={"description": "Main article page template"}
            ),
            "index_page": ContentTemplate(
                name="index_page", 
                template_path="index.html",
                template_type="html",
                metadata={"description": "Homepage template"}
            ),
            "base_layout": ContentTemplate(
                name="base_layout",
                template_path="base.html",
                template_type="html",
                metadata={"description": "Base layout template"}
            ),
            "sitemap": ContentTemplate(
                name="sitemap",
                template_path="sitemap.xml",
                template_type="xml",
                metadata={"description": "XML sitemap template"}
            ),
            "robots": ContentTemplate(
                name="robots",
                template_path="robots.txt",
                template_type="text",
                metadata={"description": "Robots.txt template"}
            )
        }
    
    def render_template(
        self,
        template_name: str,
        context: Dict[str, Any],
        **kwargs
    ) -> str:
        """
        Render a template with given context.
        
        Args:
            template_name: Name of the template to render
            context: Template context variables
            **kwargs: Additional context variables
            
        Returns:
            Rendered template content
        """
        if template_name not in self.templates:
            raise ValueError(f"Template '{template_name}' not found")
        
        # Merge context with kwargs
        full_context = {**context, **kwargs}
        
        # Add default context variables
        full_context.update({
            "site_name": "Trend Platform",
            "generated_at": datetime.utcnow(),
            "template_name": template_name
        })
        
        template = self.templates[template_name]
        
        try:
            rendered = template.render(self.env, full_context)
            
            self.logger.debug(
                "Template rendered successfully",
                template_name=template_name,
                context_keys=list(full_context.keys())
            )
            
            return rendered
        
        except Exception as e:
            self.logger.error(
                "Template rendering failed",
                template_name=template_name,
                error=str(e)
            )
            raise
    
    def render_article_page(
        self,
        title: str,
        content: str,
        meta_description: str,
        keyword: str,
        category: str,
        hero_image_url: str = None,
        code_snippet: Dict[str, str] = None,
        **kwargs
    ) -> str:
        """
        Render an article page.
        
        Args:
            title: Article title
            content: Article content (markdown)
            meta_description: SEO meta description
            keyword: Main keyword/trend
            category: Article category
            hero_image_url: URL to hero image
            code_snippet: Code snippet dict with 'language' and 'code'
            **kwargs: Additional context
            
        Returns:
            Rendered HTML page
        """
        context = {
            "title": title,
            "content": self._markdown_to_html(content),
            "meta_description": meta_description,
            "keyword": keyword,
            "category": category,
            "hero_image_url": hero_image_url,
            "code_snippet": code_snippet,
            "canonical_url": kwargs.get("canonical_url"),
            "publish_date": kwargs.get("publish_date", datetime.utcnow()),
            "author": kwargs.get("author", "Trend Platform"),
            "reading_time": self._estimate_reading_time(content),
            "word_count": len(content.split()) if content else 0
        }
        
        return self.render_template("article_page", context, **kwargs)
    
    def render_index_page(
        self,
        trending_topics: List[Dict[str, Any]],
        featured_article: Dict[str, Any] = None,
        **kwargs
    ) -> str:
        """
        Render the index/homepage.
        
        Args:
            trending_topics: List of trending topic data
            featured_article: Featured article data
            **kwargs: Additional context
            
        Returns:
            Rendered HTML page
        """
        context = {
            "trending_topics": trending_topics,
            "featured_article": featured_article,
            "page_title": "Latest Trending Topics",
            "meta_description": "Discover the latest trending topics and insights across technology, health, business, and more."
        }
        
        return self.render_template("index_page", context, **kwargs)
    
    def render_sitemap(self, urls: List[Dict[str, Any]]) -> str:
        """
        Render XML sitemap.
        
        Args:
            urls: List of URL data with 'loc', 'lastmod', 'changefreq', 'priority'
            
        Returns:
            Rendered XML sitemap
        """
        context = {
            "urls": urls,
            "lastmod": datetime.utcnow().strftime("%Y-%m-%d")
        }
        
        return self.render_template("sitemap", context)
    
    def render_robots_txt(self, sitemap_url: str, **kwargs) -> str:
        """
        Render robots.txt file.
        
        Args:
            sitemap_url: URL to the sitemap
            **kwargs: Additional directives
            
        Returns:
            Rendered robots.txt content
        """
        context = {
            "sitemap_url": sitemap_url,
            "allow_all": kwargs.get("allow_all", True),
            "disallow_paths": kwargs.get("disallow_paths", [])
        }
        
        return self.render_template("robots", context)
    
    def add_template(self, template: ContentTemplate):
        """Add a custom template."""
        self.templates[template.name] = template
        self.logger.info("Template added", template_name=template.name)
    
    def _markdown_to_html(self, markdown_content: str) -> str:
        """Convert markdown to HTML."""
        try:
            import markdown
            md = markdown.Markdown(extensions=['codehilite', 'fenced_code', 'tables'])
            return md.convert(markdown_content)
        except ImportError:
            # Fallback: basic markdown-like conversion
            html = markdown_content
            html = html.replace('\n\n', '</p><p>')
            html = html.replace('\n', '<br>')
            html = f'<p>{html}</p>'
            
            # Basic header conversion
            html = html.replace('<p># ', '<h1>').replace('</p>', '</h1>', 1)
            html = html.replace('<p>## ', '<h2>').replace('</p>', '</h2>', 1)
            html = html.replace('<p>### ', '<h3>').replace('</p>', '</h3>', 1)
            
            return html
    
    def _estimate_reading_time(self, content: str) -> int:
        """Estimate reading time in minutes."""
        words = len(content.split()) if content else 0
        # Average reading speed: 200 words per minute
        return max(1, round(words / 200))
    
    def _datetime_filter(self, dt: datetime, format: str = "%Y-%m-%d %H:%M") -> str:
        """Format datetime filter."""
        if isinstance(dt, datetime):
            return dt.strftime(format)
        return str(dt)
    
    def _truncate_words_filter(self, text: str, length: int = 50) -> str:
        """Truncate text to specified number of words."""
        if not text:
            return ""
        
        words = text.split()
        if len(words) <= length:
            return text
        
        return " ".join(words[:length]) + "..."
    
    def _slug_filter(self, text: str) -> str:
        """Convert text to URL-friendly slug."""
        import re
        slug = text.lower()
        slug = re.sub(r'[^\w\s-]', '', slug)
        slug = re.sub(r'[-\s]+', '-', slug)
        return slug.strip('-')
    
    def get_template_list(self) -> List[str]:
        """Get list of available template names."""
        return list(self.templates.keys())
    
    def validate_templates(self) -> Dict[str, bool]:
        """Validate all templates for syntax errors."""
        results = {}
        
        for name, template in self.templates.items():
            try:
                template.load_template(self.env)
                results[name] = True
            except Exception as e:
                self.logger.error(
                    "Template validation failed",
                    template_name=name,
                    error=str(e)
                )
                results[name] = False
        
        return results
