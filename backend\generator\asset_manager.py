"""
Asset management for static site generation.

Handles image optimization, asset processing, and file management
for generated static sites.
"""

import os
import asyncio
import aiohttp
import aiofiles
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from PIL import Image, ImageOps
import hashlib
from datetime import datetime
import structlog

logger = structlog.get_logger()


class ImageOptimizer:
    """Image optimization and processing utilities."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.max_width = self.config.get("max_width", 1200)
        self.max_height = self.config.get("max_height", 800)
        self.quality = self.config.get("quality", 85)
        self.webp_enabled = self.config.get("webp_enabled", True)
        self.progressive = self.config.get("progressive", True)
        
        self.logger = structlog.get_logger().bind(component="image_optimizer")
    
    async def optimize_image(
        self,
        input_path: str,
        output_path: str,
        format: str = "JPEG",
        **kwargs
    ) -> Dict[str, Any]:
        """
        Optimize an image file.
        
        Args:
            input_path: Path to input image
            output_path: Path for optimized output
            format: Output format (JPEG, PNG, WebP)
            **kwargs: Additional optimization parameters
            
        Returns:
            Optimization results and metadata
        """
        try:
            # Open and process image
            with Image.open(input_path) as img:
                # Convert to RGB if necessary
                if img.mode in ('RGBA', 'LA', 'P'):
                    if format.upper() == 'JPEG':
                        # Create white background for JPEG
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                        img = background
                    else:
                        img = img.convert('RGBA')
                elif img.mode != 'RGB' and format.upper() == 'JPEG':
                    img = img.convert('RGB')
                
                # Get original dimensions
                original_size = img.size
                original_file_size = os.path.getsize(input_path)
                
                # Resize if needed
                img = self._resize_image(img, kwargs.get("max_width", self.max_width), kwargs.get("max_height", self.max_height))
                
                # Apply additional optimizations
                if kwargs.get("auto_orient", True):
                    img = ImageOps.exif_transpose(img)
                
                # Save optimized image
                save_kwargs = self._get_save_kwargs(format, kwargs)
                img.save(output_path, format=format, **save_kwargs)
                
                # Get optimized file size
                optimized_file_size = os.path.getsize(output_path)
                
                result = {
                    "original_size": original_size,
                    "optimized_size": img.size,
                    "original_file_size": original_file_size,
                    "optimized_file_size": optimized_file_size,
                    "compression_ratio": (original_file_size - optimized_file_size) / original_file_size,
                    "format": format,
                    "output_path": output_path
                }
                
                self.logger.info(
                    "Image optimized",
                    input_path=input_path,
                    output_path=output_path,
                    compression_ratio=f"{result['compression_ratio']:.2%}",
                    size_reduction=f"{original_file_size - optimized_file_size} bytes"
                )
                
                return result
        
        except Exception as e:
            self.logger.error("Image optimization failed", input_path=input_path, error=str(e))
            raise
    
    def _resize_image(self, img: Image.Image, max_width: int, max_height: int) -> Image.Image:
        """Resize image while maintaining aspect ratio."""
        if img.size[0] <= max_width and img.size[1] <= max_height:
            return img
        
        # Calculate new size maintaining aspect ratio
        ratio = min(max_width / img.size[0], max_height / img.size[1])
        new_size = (int(img.size[0] * ratio), int(img.size[1] * ratio))
        
        # Use high-quality resampling
        return img.resize(new_size, Image.Resampling.LANCZOS)
    
    def _get_save_kwargs(self, format: str, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Get format-specific save parameters."""
        save_kwargs = {}
        
        if format.upper() == 'JPEG':
            save_kwargs.update({
                'quality': kwargs.get('quality', self.quality),
                'optimize': True,
                'progressive': kwargs.get('progressive', self.progressive)
            })
        elif format.upper() == 'PNG':
            save_kwargs.update({
                'optimize': True,
                'compress_level': kwargs.get('compress_level', 6)
            })
        elif format.upper() == 'WEBP':
            save_kwargs.update({
                'quality': kwargs.get('quality', self.quality),
                'method': kwargs.get('method', 6),
                'lossless': kwargs.get('lossless', False)
            })
        
        return save_kwargs
    
    async def create_multiple_formats(
        self,
        input_path: str,
        output_dir: str,
        base_name: str,
        formats: List[str] = None
    ) -> Dict[str, Dict[str, Any]]:
        """Create multiple optimized formats of an image."""
        formats = formats or ['JPEG', 'WebP'] if self.webp_enabled else ['JPEG']
        results = {}
        
        for format in formats:
            extension = format.lower() if format.upper() != 'JPEG' else 'jpg'
            output_path = os.path.join(output_dir, f"{base_name}.{extension}")
            
            try:
                result = await self.optimize_image(input_path, output_path, format)
                results[format.lower()] = result
            except Exception as e:
                self.logger.error(f"Failed to create {format} format", error=str(e))
        
        return results


class AssetManager:
    """Manages static assets for generated sites."""
    
    def __init__(self, base_dir: str, config: Dict[str, Any] = None):
        self.base_dir = Path(base_dir)
        self.config = config or {}
        self.image_optimizer = ImageOptimizer(config.get("image_optimization", {}))
        
        # Asset directories
        self.images_dir = self.base_dir / "images"
        self.css_dir = self.base_dir / "css"
        self.js_dir = self.base_dir / "js"
        self.fonts_dir = self.base_dir / "fonts"
        
        self.logger = structlog.get_logger().bind(component="asset_manager")
        
        # Create directories
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure all asset directories exist."""
        for directory in [self.images_dir, self.css_dir, self.js_dir, self.fonts_dir]:
            directory.mkdir(parents=True, exist_ok=True)
    
    async def download_and_optimize_image(
        self,
        url: str,
        filename: str = None,
        optimize: bool = True
    ) -> Dict[str, Any]:
        """
        Download an image from URL and optimize it.
        
        Args:
            url: Image URL to download
            filename: Optional custom filename
            optimize: Whether to optimize the image
            
        Returns:
            Asset information including local paths
        """
        try:
            # Generate filename if not provided
            if not filename:
                url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
                filename = f"image_{url_hash}"
            
            # Download image
            temp_path = await self._download_file(url, filename)
            
            if not optimize:
                # Just move to images directory
                final_path = self.images_dir / f"{filename}.jpg"
                os.rename(temp_path, final_path)
                
                return {
                    "original_url": url,
                    "local_path": str(final_path),
                    "relative_path": f"/assets/images/{final_path.name}",
                    "optimized": False
                }
            
            # Optimize image
            optimization_results = await self.image_optimizer.create_multiple_formats(
                temp_path,
                str(self.images_dir),
                filename
            )
            
            # Clean up temp file
            os.unlink(temp_path)
            
            # Prepare result
            result = {
                "original_url": url,
                "optimized": True,
                "formats": {}
            }
            
            for format, data in optimization_results.items():
                path = Path(data["output_path"])
                result["formats"][format] = {
                    "local_path": str(path),
                    "relative_path": f"/assets/images/{path.name}",
                    "file_size": data["optimized_file_size"],
                    "dimensions": data["optimized_size"]
                }
            
            # Set primary format (prefer WebP, fallback to JPEG)
            if "webp" in result["formats"]:
                result["primary"] = result["formats"]["webp"]
                result["fallback"] = result["formats"].get("jpeg", result["formats"]["webp"])
            else:
                result["primary"] = result["formats"]["jpeg"]
                result["fallback"] = result["formats"]["jpeg"]
            
            self.logger.info(
                "Image downloaded and optimized",
                url=url,
                filename=filename,
                formats=list(optimization_results.keys())
            )
            
            return result
        
        except Exception as e:
            self.logger.error("Failed to download and optimize image", url=url, error=str(e))
            raise
    
    async def _download_file(self, url: str, filename: str) -> str:
        """Download file from URL to temporary location."""
        temp_path = self.base_dir / f"temp_{filename}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                response.raise_for_status()
                
                async with aiofiles.open(temp_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)
        
        return str(temp_path)
    
    async def copy_static_assets(self, source_dir: str):
        """Copy static assets from source directory."""
        source_path = Path(source_dir)
        
        if not source_path.exists():
            self.logger.warning("Source assets directory not found", source_dir=source_dir)
            return
        
        # Copy CSS files
        css_source = source_path / "css"
        if css_source.exists():
            await self._copy_directory(css_source, self.css_dir)
        
        # Copy JS files
        js_source = source_path / "js"
        if js_source.exists():
            await self._copy_directory(js_source, self.js_dir)
        
        # Copy fonts
        fonts_source = source_path / "fonts"
        if fonts_source.exists():
            await self._copy_directory(fonts_source, self.fonts_dir)
        
        self.logger.info("Static assets copied", source_dir=source_dir)
    
    async def _copy_directory(self, source: Path, destination: Path):
        """Recursively copy directory contents."""
        for item in source.rglob("*"):
            if item.is_file():
                relative_path = item.relative_to(source)
                dest_path = destination / relative_path
                dest_path.parent.mkdir(parents=True, exist_ok=True)
                
                async with aiofiles.open(item, 'rb') as src, aiofiles.open(dest_path, 'wb') as dst:
                    await dst.write(await src.read())
    
    def generate_css_file(self, css_content: str, filename: str = "main.css") -> str:
        """Generate CSS file with content."""
        css_path = self.css_dir / filename
        
        with open(css_path, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        self.logger.info("CSS file generated", filename=filename)
        return str(css_path)
    
    def generate_js_file(self, js_content: str, filename: str = "main.js") -> str:
        """Generate JavaScript file with content."""
        js_path = self.js_dir / filename
        
        with open(js_path, 'w', encoding='utf-8') as f:
            f.write(js_content)
        
        self.logger.info("JavaScript file generated", filename=filename)
        return str(js_path)
    
    def get_asset_manifest(self) -> Dict[str, Any]:
        """Generate manifest of all assets."""
        manifest = {
            "generated_at": datetime.utcnow().isoformat(),
            "assets": {
                "images": [],
                "css": [],
                "js": [],
                "fonts": []
            }
        }
        
        # Scan each asset directory
        for asset_type, directory in [
            ("images", self.images_dir),
            ("css", self.css_dir),
            ("js", self.js_dir),
            ("fonts", self.fonts_dir)
        ]:
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    relative_path = file_path.relative_to(self.base_dir)
                    manifest["assets"][asset_type].append({
                        "path": str(relative_path),
                        "size": file_path.stat().st_size,
                        "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat()
                    })
        
        return manifest
    
    def cleanup_old_assets(self, max_age_days: int = 30):
        """Clean up old unused assets."""
        cutoff_time = datetime.now().timestamp() - (max_age_days * 24 * 60 * 60)
        cleaned_count = 0
        
        for directory in [self.images_dir, self.css_dir, self.js_dir]:
            for file_path in directory.rglob("*"):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                    except Exception as e:
                        self.logger.warning("Failed to delete old asset", file=str(file_path), error=str(e))
        
        self.logger.info("Asset cleanup completed", cleaned_count=cleaned_count)
        return cleaned_count
