# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/trend_platform
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# AI Services Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_API_KEY=your_anthropic_api_key

# Authentication
JWT_SECRET_KEY=your_jwt_secret_key_here_make_it_long_and_random
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# NextAuth Configuration (for frontend)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_key_here

# Scraping Configuration
GOOGLE_TRENDS_API_KEY=your_google_trends_api_key
PROXY_LIST=proxy1:port,proxy2:port,proxy3:port

# Git Repository Configuration
CONTENT_REPO_URL=https://github.com/user/content-repo.git
GITHUB_TOKEN=your_github_token
GITHUB_WEBHOOK_SECRET=your_webhook_secret

# Coolify Configuration
COOLIFY_API_URL=https://deploy.yourdomain.com
COOLIFY_API_TOKEN=your_coolify_api_token
COOLIFY_TEAM_ID=your_team_id
COOLIFY_APP_UUID=your_application_uuid

# Cloudflare Configuration
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ZONE_ID=your_zone_id
CLOUDFLARE_ACCOUNT_ID=your_account_id

# Monitoring Configuration
GRAFANA_URL=http://localhost:3001
PROMETHEUS_URL=http://localhost:9090
LOKI_URL=http://localhost:3100

# Security Configuration
VAULT_URL=http://localhost:8200
VAULT_TOKEN=your_vault_token

# Application Configuration
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO
API_V1_PREFIX=/api/v1

# Content Generation Configuration
MAX_ARTICLE_LENGTH=2000
MIN_ARTICLE_LENGTH=500
CONTENT_EXPIRY_DAYS=14
IMAGE_OPTIMIZATION_ENABLED=true
WEBP_CONVERSION_ENABLED=true

# Rate Limiting Configuration
RATE_LIMIT_REQUESTS_PER_MINUTE=60
SCRAPER_RATE_LIMIT_RPM=10

# Build Configuration
NODE_VERSION=18
BUILD_TIMEOUT=600
HEALTH_CHECK_TIMEOUT=30
