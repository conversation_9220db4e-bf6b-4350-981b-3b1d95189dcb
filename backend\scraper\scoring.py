"""
Scoring utilities for trend data.

Provides comprehensive scoring algorithms to rank and prioritize trends
based on multiple factors including search volume, growth rate, and quality metrics.
"""

import math
from typing import List, Dict, Optional, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
import structlog

from .sources.base_scraper import TrendData

logger = structlog.get_logger()


@dataclass
class ScoringWeights:
    """Configuration for scoring weights."""
    search_volume: float = 0.4
    growth_rate: float = 0.3
    recency: float = 0.1
    source_reliability: float = 0.1
    keyword_quality: float = 0.05
    category_boost: float = 0.05


class TrendScorer:
    """Main scoring engine for trend data."""
    
    def __init__(
        self,
        weights: ScoringWeights = None,
        custom_scorers: List[Callable] = None
    ):
        """
        Initialize trend scorer.
        
        Args:
            weights: Scoring weights configuration
            custom_scorers: List of custom scoring functions
        """
        self.weights = weights or ScoringWeights()
        self.custom_scorers = custom_scorers or []
        
        # Source reliability mapping
        self.source_reliability = {
            "google_trends": 0.95,
            "trends24": 0.85,
            "custom": 0.70,
            "unknown": 0.50
        }
        
        # Category boost mapping
        self.category_boosts = {
            "Technology": 1.2,
            "Health": 1.1,
            "Business": 1.1,
            "Entertainment": 1.0,
            "Science": 1.1,
            "Sports": 0.9,
            "Politics": 0.8,
            "General": 1.0
        }
        
        self.logger = structlog.get_logger().bind(component="trend_scorer")
    
    def score_trend(self, trend: TrendData) -> float:
        """
        Calculate comprehensive score for a single trend.
        
        Args:
            trend: Trend data to score
            
        Returns:
            Normalized score between 0 and 100
        """
        # Calculate individual component scores
        search_volume_score = self._score_search_volume(trend)
        growth_rate_score = self._score_growth_rate(trend)
        recency_score = self._score_recency(trend)
        source_score = self._score_source_reliability(trend)
        keyword_score = self._score_keyword_quality(trend)
        category_score = self._score_category(trend)
        
        # Calculate weighted total
        total_score = (
            search_volume_score * self.weights.search_volume +
            growth_rate_score * self.weights.growth_rate +
            recency_score * self.weights.recency +
            source_score * self.weights.source_reliability +
            keyword_score * self.weights.keyword_quality +
            category_score * self.weights.category_boost
        )
        
        # Apply custom scorers
        for custom_scorer in self.custom_scorers:
            try:
                custom_score = custom_scorer(trend)
                total_score += custom_score
            except Exception as e:
                self.logger.warning("Custom scorer failed", error=str(e))
        
        # Normalize to 0-100 scale
        final_score = max(0, min(100, total_score * 100))
        
        self.logger.debug(
            "Trend scored",
            keyword=trend.keyword,
            final_score=final_score,
            components={
                "search_volume": search_volume_score,
                "growth_rate": growth_rate_score,
                "recency": recency_score,
                "source": source_score,
                "keyword": keyword_score,
                "category": category_score
            }
        )
        
        return final_score
    
    def score_trends(self, trends: List[TrendData]) -> List[TrendData]:
        """
        Score multiple trends and sort by score.
        
        Args:
            trends: List of trends to score
            
        Returns:
            List of trends sorted by score (highest first)
        """
        self.logger.info("Scoring trends", trend_count=len(trends))
        
        # Score each trend
        scored_trends = []
        for trend in trends:
            score = self.score_trend(trend)
            # Store score in raw_data for later use
            if trend.raw_data is None:
                trend.raw_data = {}
            trend.raw_data['calculated_score'] = score
            scored_trends.append((score, trend))
        
        # Sort by score (highest first)
        scored_trends.sort(key=lambda x: x[0], reverse=True)
        
        # Extract trends in sorted order
        sorted_trends = [trend for _, trend in scored_trends]
        
        # Log scoring statistics
        scores = [score for score, _ in scored_trends]
        avg_score = sum(scores) / len(scores) if scores else 0
        max_score = max(scores) if scores else 0
        min_score = min(scores) if scores else 0
        
        self.logger.info(
            "Trend scoring completed",
            trend_count=len(trends),
            avg_score=avg_score,
            max_score=max_score,
            min_score=min_score
        )
        
        return sorted_trends
    
    def _score_search_volume(self, trend: TrendData) -> float:
        """Score based on search volume."""
        if not trend.search_volume:
            return 0.0
        
        # Use logarithmic scaling to handle wide range of volumes
        # Assume max volume of 10M for normalization
        max_volume = 10_000_000
        normalized_volume = min(trend.search_volume, max_volume) / max_volume
        
        # Apply logarithmic scaling
        if normalized_volume > 0:
            log_score = math.log10(normalized_volume * 9 + 1)  # Scale to 0-1
            return log_score
        
        return 0.0
    
    def _score_growth_rate(self, trend: TrendData) -> float:
        """Score based on growth rate."""
        if not trend.growth_rate:
            return 0.5  # Neutral score for missing data
        
        # Normalize growth rate (assume max meaningful growth of 1000%)
        max_growth = 10.0
        normalized_growth = min(trend.growth_rate, max_growth) / max_growth
        
        # Apply sigmoid function to give diminishing returns for very high growth
        sigmoid_score = 2 / (1 + math.exp(-5 * normalized_growth)) - 1
        return max(0, min(1, sigmoid_score))
    
    def _score_recency(self, trend: TrendData) -> float:
        """Score based on how recent the trend data is."""
        if not trend.timestamp:
            return 0.5  # Neutral score for missing timestamp
        
        # Calculate age in hours
        age_hours = (datetime.utcnow() - trend.timestamp).total_seconds() / 3600
        
        # Exponential decay with half-life of 12 hours
        half_life = 12.0
        decay_factor = math.exp(-age_hours * math.log(2) / half_life)
        
        return max(0, min(1, decay_factor))
    
    def _score_source_reliability(self, trend: TrendData) -> float:
        """Score based on source reliability."""
        return self.source_reliability.get(trend.source, 0.5)
    
    def _score_keyword_quality(self, trend: TrendData) -> float:
        """Score based on keyword quality indicators."""
        if not trend.keyword:
            return 0.0
        
        keyword = trend.keyword.strip()
        score = 0.0
        
        # Length score (prefer 2-5 words)
        word_count = len(keyword.split())
        if 2 <= word_count <= 5:
            score += 0.4
        elif word_count == 1:
            score += 0.2
        else:
            score += 0.1
        
        # Character diversity score
        unique_chars = len(set(keyword.lower()))
        total_chars = len(keyword)
        if total_chars > 0:
            diversity = unique_chars / total_chars
            score += diversity * 0.3
        
        # Avoid all caps (likely spam)
        if not keyword.isupper():
            score += 0.2
        
        # Avoid excessive punctuation
        punct_ratio = sum(1 for c in keyword if not c.isalnum() and not c.isspace()) / len(keyword)
        if punct_ratio < 0.2:
            score += 0.1
        
        return min(1.0, score)
    
    def _score_category(self, trend: TrendData) -> float:
        """Score based on category importance."""
        if not trend.category:
            return 1.0  # Neutral score
        
        boost = self.category_boosts.get(trend.category, 1.0)
        # Convert boost to 0-1 score (1.0 boost = 0.5 score, 1.2 boost = 0.6 score)
        return min(1.0, boost / 2.0)


class AdaptiveScorer:
    """Adaptive scoring that learns from user feedback and performance."""
    
    def __init__(self, base_scorer: TrendScorer = None):
        self.base_scorer = base_scorer or TrendScorer()
        self.feedback_data = []
        self.performance_history = {}
        self.adaptation_enabled = True
        
        self.logger = structlog.get_logger().bind(component="adaptive_scorer")
    
    def record_feedback(
        self,
        trend: TrendData,
        feedback_type: str,
        feedback_value: float,
        context: Dict[str, Any] = None
    ):
        """
        Record user feedback for trend scoring adaptation.
        
        Args:
            trend: The trend that received feedback
            feedback_type: Type of feedback ('approval', 'rejection', 'engagement')
            feedback_value: Numeric feedback value (0-1 scale)
            context: Additional context information
        """
        feedback_record = {
            "trend_keyword": trend.keyword,
            "trend_category": trend.category,
            "trend_source": trend.source,
            "original_score": trend.raw_data.get('calculated_score', 0),
            "feedback_type": feedback_type,
            "feedback_value": feedback_value,
            "timestamp": datetime.utcnow(),
            "context": context or {}
        }
        
        self.feedback_data.append(feedback_record)
        
        # Keep only recent feedback (last 1000 records)
        if len(self.feedback_data) > 1000:
            self.feedback_data = self.feedback_data[-1000:]
        
        self.logger.info(
            "Feedback recorded",
            keyword=trend.keyword,
            feedback_type=feedback_type,
            feedback_value=feedback_value
        )
    
    def adapt_weights(self):
        """Adapt scoring weights based on accumulated feedback."""
        if not self.adaptation_enabled or len(self.feedback_data) < 50:
            return
        
        self.logger.info("Starting weight adaptation", feedback_count=len(self.feedback_data))
        
        # Analyze feedback patterns
        category_performance = self._analyze_category_performance()
        source_performance = self._analyze_source_performance()
        
        # Adjust category boosts
        for category, performance in category_performance.items():
            if performance > 0.7:  # High performance
                self.base_scorer.category_boosts[category] *= 1.1
            elif performance < 0.3:  # Low performance
                self.base_scorer.category_boosts[category] *= 0.9
        
        # Adjust source reliability
        for source, performance in source_performance.items():
            if performance > 0.7:
                self.base_scorer.source_reliability[source] = min(1.0, self.base_scorer.source_reliability.get(source, 0.5) * 1.1)
            elif performance < 0.3:
                self.base_scorer.source_reliability[source] = max(0.1, self.base_scorer.source_reliability.get(source, 0.5) * 0.9)
        
        self.logger.info("Weight adaptation completed")
    
    def _analyze_category_performance(self) -> Dict[str, float]:
        """Analyze performance by category."""
        category_feedback = {}
        
        for feedback in self.feedback_data:
            category = feedback["trend_category"]
            if category not in category_feedback:
                category_feedback[category] = []
            category_feedback[category].append(feedback["feedback_value"])
        
        # Calculate average performance per category
        category_performance = {}
        for category, values in category_feedback.items():
            if values:
                category_performance[category] = sum(values) / len(values)
        
        return category_performance
    
    def _analyze_source_performance(self) -> Dict[str, float]:
        """Analyze performance by source."""
        source_feedback = {}
        
        for feedback in self.feedback_data:
            source = feedback["trend_source"]
            if source not in source_feedback:
                source_feedback[source] = []
            source_feedback[source].append(feedback["feedback_value"])
        
        # Calculate average performance per source
        source_performance = {}
        for source, values in source_feedback.items():
            if values:
                source_performance[source] = sum(values) / len(values)
        
        return source_performance
    
    def score_trend(self, trend: TrendData) -> float:
        """Score trend using adaptive scoring."""
        # Use base scorer
        base_score = self.base_scorer.score_trend(trend)
        
        # Apply adaptations if enabled
        if self.adaptation_enabled and len(self.feedback_data) >= 10:
            # Periodic adaptation
            if len(self.feedback_data) % 100 == 0:
                self.adapt_weights()
        
        return base_score
    
    def get_adaptation_stats(self) -> Dict[str, Any]:
        """Get adaptation statistics."""
        return {
            "feedback_count": len(self.feedback_data),
            "adaptation_enabled": self.adaptation_enabled,
            "category_boosts": dict(self.base_scorer.category_boosts),
            "source_reliability": dict(self.base_scorer.source_reliability),
            "recent_feedback_avg": sum(f["feedback_value"] for f in self.feedback_data[-50:]) / min(50, len(self.feedback_data)) if self.feedback_data else 0
        }
