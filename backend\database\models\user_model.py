"""
User profile model and repository implementation.

Handles all database operations related to user profiles including
CRUD operations, role management, and user-specific queries.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr
import asyncpg
from .base_model import BaseRepository
import logging

logger = logging.getLogger(__name__)


class UserProfileData(BaseModel):
    """Pydantic model for user profile data validation."""
    id: str  # UUID from Supabase auth.users
    email: Optional[EmailStr] = None
    role: str = Field(default='viewer', pattern='^(admin|editor|viewer)$')
    permissions: List[str] = Field(default_factory=list)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class UserProfileRepository(BaseRepository[UserProfileData]):
    """Repository for user profile-related database operations."""
    
    def __init__(self, connection_pool: asyncpg.Pool):
        super().__init__(connection_pool, 'user_profiles')
    
    async def get_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Get user profile by email (requires join with auth.users)."""
        query = """
            SELECT 
                up.*,
                au.email
            FROM user_profiles up
            JOIN auth.users au ON up.id = au.id
            WHERE au.email = $1
        """
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, email)
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"Failed to get user profile by email {email}: {e}")
            raise
    
    async def get_users_by_role(self, role: str) -> List[Dict[str, Any]]:
        """Get all users with a specific role."""
        query = """
            SELECT 
                up.*,
                au.email
            FROM user_profiles up
            JOIN auth.users au ON up.id = au.id
            WHERE up.role = $1
            ORDER BY up.created_at DESC
        """
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, role)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get users by role {role}: {e}")
            raise
    
    async def get_admins(self) -> List[Dict[str, Any]]:
        """Get all admin users."""
        return await self.get_users_by_role('admin')
    
    async def get_editors(self) -> List[Dict[str, Any]]:
        """Get all editor users."""
        return await self.get_users_by_role('editor')
    
    async def update_role(self, user_id: str, new_role: str, updated_by: str = None) -> bool:
        """Update user role."""
        valid_roles = ['admin', 'editor', 'viewer']
        if new_role not in valid_roles:
            raise ValueError(f"Invalid role. Must be one of: {valid_roles}")
        
        update_data = {
            'role': new_role,
            'updated_at': datetime.utcnow()
        }
        
        try:
            result = await self.update(user_id, update_data)
            if result:
                logger.info(f"Updated user {user_id} role to {new_role} by {updated_by}")
            return result
        except Exception as e:
            logger.error(f"Failed to update role for user {user_id}: {e}")
            raise
    
    async def add_permission(self, user_id: str, permission: str) -> bool:
        """Add a permission to user."""
        query = """
            UPDATE user_profiles 
            SET permissions = array_append(permissions, $2),
                updated_at = NOW()
            WHERE id = $1 AND NOT ($2 = ANY(permissions))
        """
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.execute(query, user_id, permission)
                updated = result.split()[-1] == '1'
                if updated:
                    logger.info(f"Added permission {permission} to user {user_id}")
                return updated
        except Exception as e:
            logger.error(f"Failed to add permission {permission} to user {user_id}: {e}")
            raise
    
    async def remove_permission(self, user_id: str, permission: str) -> bool:
        """Remove a permission from user."""
        query = """
            UPDATE user_profiles 
            SET permissions = array_remove(permissions, $2),
                updated_at = NOW()
            WHERE id = $1
        """
        
        try:
            async with self.pool.acquire() as conn:
                result = await conn.execute(query, user_id, permission)
                updated = result.split()[-1] == '1'
                if updated:
                    logger.info(f"Removed permission {permission} from user {user_id}")
                return updated
        except Exception as e:
            logger.error(f"Failed to remove permission {permission} from user {user_id}: {e}")
            raise
    
    async def has_permission(self, user_id: str, permission: str) -> bool:
        """Check if user has a specific permission."""
        query = """
            SELECT $2 = ANY(permissions) as has_permission
            FROM user_profiles 
            WHERE id = $1
        """
        
        try:
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(query, user_id, permission)
                return row['has_permission'] if row else False
        except Exception as e:
            logger.error(f"Failed to check permission {permission} for user {user_id}: {e}")
            raise
    
    async def get_user_stats(self) -> Dict[str, Any]:
        """Get user statistics."""
        stats_query = """
            SELECT 
                COUNT(*) as total_users,
                COUNT(CASE WHEN role = 'admin' THEN 1 END) as admin_count,
                COUNT(CASE WHEN role = 'editor' THEN 1 END) as editor_count,
                COUNT(CASE WHEN role = 'viewer' THEN 1 END) as viewer_count,
                COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as recent_users_count
            FROM user_profiles
        """
        
        role_query = """
            SELECT 
                role,
                COUNT(*) as count
            FROM user_profiles
            GROUP BY role
            ORDER BY count DESC
        """
        
        try:
            async with self.pool.acquire() as conn:
                # Get general stats
                stats_row = await conn.fetchrow(stats_query)
                stats = dict(stats_row) if stats_row else {}
                
                # Get role breakdown
                role_rows = await conn.fetch(role_query)
                stats['users_by_role'] = {
                    row['role']: row['count'] 
                    for row in role_rows
                }
                
                return stats
        except Exception as e:
            logger.error(f"Failed to get user stats: {e}")
            raise
    
    async def search_users(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search users by email or other criteria."""
        search_query = """
            SELECT 
                up.*,
                au.email
            FROM user_profiles up
            JOIN auth.users au ON up.id = au.id
            WHERE au.email ILIKE $1
            ORDER BY up.created_at DESC
            LIMIT $2
        """
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(search_query, f"%{query}%", limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to search users with query '{query}': {e}")
            raise
    
    async def get_recent_users(self, days: int = 30, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recently registered users."""
        query = """
            SELECT 
                up.*,
                au.email
            FROM user_profiles up
            JOIN auth.users au ON up.id = au.id
            WHERE up.created_at >= NOW() - INTERVAL '%s days'
            ORDER BY up.created_at DESC
            LIMIT $1
        """ % days
        
        try:
            async with self.pool.acquire() as conn:
                rows = await conn.fetch(query, limit)
                return [dict(row) for row in rows]
        except Exception as e:
            logger.error(f"Failed to get recent users: {e}")
            raise
    
    async def create_user_profile(self, user_id: str, email: str, role: str = 'viewer') -> str:
        """Create a new user profile (typically called after Supabase auth signup)."""
        valid_roles = ['admin', 'editor', 'viewer']
        if role not in valid_roles:
            raise ValueError(f"Invalid role. Must be one of: {valid_roles}")
        
        profile_data = {
            'id': user_id,
            'role': role,
            'permissions': [],
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        try:
            # Use the user_id as the primary key (same as auth.users.id)
            query = """
                INSERT INTO user_profiles (id, role, permissions, created_at, updated_at)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id
            """
            
            async with self.pool.acquire() as conn:
                row = await conn.fetchrow(
                    query,
                    profile_data['id'],
                    profile_data['role'],
                    profile_data['permissions'],
                    profile_data['created_at'],
                    profile_data['updated_at']
                )
                created_id = str(row['id'])
                logger.info(f"Created user profile for {email} with role {role}")
                return created_id
        except Exception as e:
            logger.error(f"Failed to create user profile for {email}: {e}")
            raise
    
    async def delete_user_profile(self, user_id: str) -> bool:
        """Delete user profile (typically called when user account is deleted)."""
        try:
            result = await self.delete(user_id)
            if result:
                logger.info(f"Deleted user profile {user_id}")
            return result
        except Exception as e:
            logger.error(f"Failed to delete user profile {user_id}: {e}")
            raise
