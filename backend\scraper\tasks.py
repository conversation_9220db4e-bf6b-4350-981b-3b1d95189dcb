"""
Celery tasks for trend scraping operations.

Provides asynchronous task execution for trend scraping, processing,
and related operations using Celery distributed task queue.
"""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from celery import Celery
from celery.schedules import crontab
import structlog

from .core import TrendOrchestrator
from database.connection import get_database_pool
from database.models.trend_model import TrendRepository
from app.config import settings

logger = structlog.get_logger()

# Initialize Celery app
celery_app = Celery(
    'trend_scraper',
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend
)

# Celery configuration
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Periodic task schedule
celery_app.conf.beat_schedule = {
    'scrape-trends-hourly': {
        'task': 'scraper.tasks.scrape_trends_task',
        'schedule': crontab(minute=0),  # Every hour
        'args': (['US', 'UK', 'CA'], ['Technology', 'Health', 'Business'])
    },
    'scrape-trends-daily-full': {
        'task': 'scraper.tasks.scrape_trends_comprehensive',
        'schedule': crontab(hour=6, minute=0),  # Daily at 6 AM UTC
        'args': ()
    },
    'cleanup-old-trends': {
        'task': 'scraper.tasks.cleanup_old_trends_task',
        'schedule': crontab(hour=2, minute=0),  # Daily at 2 AM UTC
        'args': (30,)  # Keep trends for 30 days
    },
    'update-trend-scores': {
        'task': 'scraper.tasks.update_trend_scores_task',
        'schedule': crontab(hour=4, minute=0),  # Daily at 4 AM UTC
        'args': ()
    }
}


async def get_trend_repository() -> TrendRepository:
    """Get trend repository instance."""
    pool = await get_database_pool()
    return TrendRepository(pool)


@celery_app.task(bind=True, name='scraper.tasks.scrape_trends_task')
def scrape_trends_task(
    self,
    regions: List[str] = None,
    categories: List[str] = None,
    max_trends_per_source: int = 50
):
    """
    Celery task for scraping trends from all sources.
    
    Args:
        regions: List of regions to scrape
        categories: List of categories to scrape
        max_trends_per_source: Maximum trends per source
    """
    task_id = self.request.id
    logger.info("Starting trend scraping task", task_id=task_id)
    
    try:
        # Run async scraping in event loop
        result = asyncio.run(_run_scraping_pipeline(regions, categories, max_trends_per_source))
        
        logger.info(
            "Trend scraping task completed",
            task_id=task_id,
            success=result["success"],
            trends_stored=result["statistics"]["total_stored"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Trend scraping task failed", task_id=task_id, error=str(e))
        raise


async def _run_scraping_pipeline(
    regions: List[str] = None,
    categories: List[str] = None,
    max_trends_per_source: int = 50
) -> Dict[str, Any]:
    """Run the scraping pipeline asynchronously."""
    trend_repo = await get_trend_repository()
    orchestrator = TrendOrchestrator(trend_repo)
    
    return await orchestrator.run_full_pipeline(
        regions=regions,
        categories=categories,
        max_trends_per_source=max_trends_per_source
    )


@celery_app.task(bind=True, name='scraper.tasks.scrape_trends_comprehensive')
def scrape_trends_comprehensive(self):
    """
    Comprehensive daily trend scraping task.
    
    Scrapes from all regions and categories with higher limits.
    """
    task_id = self.request.id
    logger.info("Starting comprehensive trend scraping", task_id=task_id)
    
    # All supported regions and categories
    all_regions = ['US', 'UK', 'CA', 'AU']
    all_categories = ['Technology', 'Health', 'Entertainment', 'Business', 'Science']
    
    try:
        result = asyncio.run(_run_scraping_pipeline(
            regions=all_regions,
            categories=all_categories,
            max_trends_per_source=100
        ))
        
        logger.info(
            "Comprehensive scraping completed",
            task_id=task_id,
            trends_stored=result["statistics"]["total_stored"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Comprehensive scraping failed", task_id=task_id, error=str(e))
        raise


@celery_app.task(bind=True, name='scraper.tasks.scrape_single_source')
def scrape_single_source_task(
    self,
    source_name: str,
    region: str,
    category: str = None
):
    """
    Scrape trends from a single source for testing or targeted scraping.
    
    Args:
        source_name: Name of the scraper source ('google_trends', 'trends24')
        region: Region to scrape
        category: Optional category filter
    """
    task_id = self.request.id
    logger.info(
        "Starting single source scraping",
        task_id=task_id,
        source=source_name,
        region=region,
        category=category
    )
    
    try:
        result = asyncio.run(_run_single_source_scraping(source_name, region, category))
        
        logger.info(
            "Single source scraping completed",
            task_id=task_id,
            source=source_name,
            trends_found=len(result)
        )
        
        return {
            "source": source_name,
            "region": region,
            "category": category,
            "trends_found": len(result),
            "trends": [trend.to_dict() for trend in result]
        }
    
    except Exception as e:
        logger.error(
            "Single source scraping failed",
            task_id=task_id,
            source=source_name,
            error=str(e)
        )
        raise


async def _run_single_source_scraping(
    source_name: str,
    region: str,
    category: str = None
) -> List:
    """Run single source scraping."""
    trend_repo = await get_trend_repository()
    orchestrator = TrendOrchestrator(trend_repo)
    
    if source_name not in orchestrator.scrapers:
        raise ValueError(f"Unknown scraper source: {source_name}")
    
    scraper = orchestrator.scrapers[source_name]
    
    async with scraper:
        trends = await scraper.scrape_trends(region, category)
    
    return trends


@celery_app.task(bind=True, name='scraper.tasks.cleanup_old_trends_task')
def cleanup_old_trends_task(self, days_to_keep: int = 30):
    """
    Clean up old trend data.
    
    Args:
        days_to_keep: Number of days of trend data to keep
    """
    task_id = self.request.id
    logger.info("Starting trend cleanup", task_id=task_id, days_to_keep=days_to_keep)
    
    try:
        result = asyncio.run(_cleanup_old_trends(days_to_keep))
        
        logger.info(
            "Trend cleanup completed",
            task_id=task_id,
            trends_cleaned=result["cleaned_count"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Trend cleanup failed", task_id=task_id, error=str(e))
        raise


async def _cleanup_old_trends(days_to_keep: int) -> Dict[str, Any]:
    """Clean up old trends."""
    trend_repo = await get_trend_repository()
    
    # Calculate cutoff date
    cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
    
    # Get old trends
    old_trends = await trend_repo.list_with_pagination(
        filters={"created_at__lt": cutoff_date},
        page_size=1000
    )
    
    cleaned_count = 0
    for trend in old_trends["data"]:
        try:
            await trend_repo.delete(trend["id"])
            cleaned_count += 1
        except Exception as e:
            logger.warning("Failed to delete trend", trend_id=trend["id"], error=str(e))
    
    return {
        "cutoff_date": cutoff_date.isoformat(),
        "trends_found": len(old_trends["data"]),
        "cleaned_count": cleaned_count
    }


@celery_app.task(bind=True, name='scraper.tasks.update_trend_scores_task')
def update_trend_scores_task(self):
    """
    Recalculate scores for existing trends.
    
    Useful when scoring algorithms are updated.
    """
    task_id = self.request.id
    logger.info("Starting trend score update", task_id=task_id)
    
    try:
        result = asyncio.run(_update_trend_scores())
        
        logger.info(
            "Trend score update completed",
            task_id=task_id,
            trends_updated=result["updated_count"]
        )
        
        return result
    
    except Exception as e:
        logger.error("Trend score update failed", task_id=task_id, error=str(e))
        raise


async def _update_trend_scores() -> Dict[str, Any]:
    """Update scores for existing trends."""
    trend_repo = await get_trend_repository()
    orchestrator = TrendOrchestrator(trend_repo)
    
    # Get all pending and approved trends
    trends_data = await trend_repo.list_with_pagination(
        filters={"status__in": ["pending", "approved"]},
        page_size=1000
    )
    
    updated_count = 0
    
    for trend_data in trends_data["data"]:
        try:
            # Convert to TrendData object
            from .sources.base_scraper import TrendData
            trend = TrendData(
                keyword=trend_data["keyword"],
                search_volume=trend_data.get("search_volume"),
                growth_rate=trend_data.get("growth_rate"),
                region=trend_data["region"],
                category=trend_data["category"],
                source=trend_data["source"],
                raw_data=trend_data.get("raw_data", {})
            )
            
            # Calculate new score
            new_score = orchestrator.scorer.score_trend(trend)
            
            # Update in database
            await trend_repo.update(trend_data["id"], {"score": new_score})
            updated_count += 1
            
        except Exception as e:
            logger.warning(
                "Failed to update trend score",
                trend_id=trend_data["id"],
                error=str(e)
            )
    
    return {
        "trends_processed": len(trends_data["data"]),
        "updated_count": updated_count
    }


@celery_app.task(bind=True, name='scraper.tasks.test_scrapers_task')
def test_scrapers_task(self):
    """
    Test all scrapers for connectivity and basic functionality.
    """
    task_id = self.request.id
    logger.info("Starting scraper testing", task_id=task_id)
    
    try:
        result = asyncio.run(_test_all_scrapers())
        
        logger.info(
            "Scraper testing completed",
            task_id=task_id,
            working_scrapers=sum(1 for r in result["results"] if r["working"])
        )
        
        return result
    
    except Exception as e:
        logger.error("Scraper testing failed", task_id=task_id, error=str(e))
        raise


async def _test_all_scrapers() -> Dict[str, Any]:
    """Test all scrapers."""
    trend_repo = await get_trend_repository()
    orchestrator = TrendOrchestrator(trend_repo)
    
    results = []
    
    for source_name, scraper in orchestrator.scrapers.items():
        try:
            async with scraper:
                # Test connection
                connection_ok = await scraper.test_connection()
                
                # Test basic scraping
                test_trends = []
                if connection_ok:
                    test_trends = await scraper.scrape_trends("US", "Technology")
                
                results.append({
                    "source": source_name,
                    "working": connection_ok and len(test_trends) > 0,
                    "connection_ok": connection_ok,
                    "test_trends_count": len(test_trends),
                    "error": None
                })
        
        except Exception as e:
            results.append({
                "source": source_name,
                "working": False,
                "connection_ok": False,
                "test_trends_count": 0,
                "error": str(e)
            })
    
    return {
        "test_timestamp": datetime.utcnow().isoformat(),
        "total_scrapers": len(orchestrator.scrapers),
        "working_scrapers": sum(1 for r in results if r["working"]),
        "results": results
    }


# Task routing
celery_app.conf.task_routes = {
    'scraper.tasks.*': {'queue': 'scraper'},
}

# Error handling
@celery_app.task(bind=True)
def handle_scraper_error(self, task_id: str, error_message: str):
    """Handle scraper task errors."""
    logger.error(
        "Scraper task error handler",
        failed_task_id=task_id,
        error=error_message
    )
    
    # Could implement error notification, retry logic, etc.
    return {
        "error_handled": True,
        "failed_task_id": task_id,
        "error_message": error_message,
        "timestamp": datetime.utcnow().isoformat()
    }
